{"compilerOptions": {"target": "ES2020", "moduleResolution": "Node", "baseUrl": ".", "paths": {"@constants": ["src/constants/index.ts"], "@constants/*": ["src/constants/*"], "@shared/*": ["src/shared/*"], "@file-ops/*": ["src/file-ops/*"], "@lib/*": ["src/lib/*"]}, "strict": true, "noImplicitAny": true, "skipLibCheck": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": false, "sourceMap": true, "incremental": true}}