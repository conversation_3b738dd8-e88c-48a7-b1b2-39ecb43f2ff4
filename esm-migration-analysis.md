## ESM Migration Analysis for Electron Main Build

### Overview
This document verifies the current build architecture and evaluates migrating the Electron main process from CommonJS (CJS) to ECMAScript Modules (ESM). It also outlines risks and a recommended approach.

---

## Phase 1: Architecture Verification

### 1) Build description vs. codebase (updated)
Current build:
- Vite production build for the renderer (dist/)
- tsup builds Electron main as ESM (build/main/main.mjs) and compiles preload/worker as CJS (build/main)
- TypeScript compile for packaging scripts to CommonJS (build/scripts)

Findings:
- Renderer (Vite)
  - vite.config.ts present and configured for React, worker ES format, wasm plugin, top-level await, paths. Production builds go to dist/ (default Vite output confirmed by existing contents).
  - package.json scripts: "build": "vite build" and packaging scripts invoke Vite first.
- Electron main/preload (tsup)
  - tsup.config.ts defines two builds: ESM main entry and CJS preload/worker artifacts to build/main.
  - package.json scripts: "build:main:esm": tsup --config tsup.config.ts
  - package.json main entry: "main": "build/main/main.mjs".
- Packaging scripts (TypeScript → CJS)
  - tsconfig.scripts.json sets "module": "CommonJS", "outDir": "build/scripts".
  - package.json scripts: "build:scripts": tsc -p tsconfig.scripts.json.
- Packaging (electron-builder)
  - electron-builder config is in package.json under "build". "files" includes dist/**/* and build/**/*; afterSign points to compiled CJS hook "build/scripts/notarize.js".

Conclusion: The stated build description is accurate.

### 2) Output directories and validation
Observed directories and representative files:
- dist/
  - index.html, various assets, and ES workers (e.g., assets/*-worker-*.js), wasm assets, and CSS/JS bundles present.
- build/main/
  - main.mjs, preload.mjs, db/database-worker.mjs, plus compiled main-side modules and maps.
- build/scripts/
  - notarize.js, build-main-ts.js, verify-build.js, release-checklist.js, etc.

Conclusion: Outputs match the expected structure and indicate a successful prior build consistent with the current configuration.

### 3) Relevant configuration and control files
- package.json
  - scripts: build, build:main:esm, build:scripts, package/release; main entry points to build/main/main.mjs
  - electron-builder configuration under "build" (output: release-builds; files: dist, build; afterSign: build/scripts/notarize.js)
- vite.config.ts (renderer)
- tsup.config.ts (ESM main + CJS preload/worker)
- tsconfig.scripts.json (scripts → CJS in build/scripts)
- tsconfig.json (renderer tsconfig; excludes src/main/**)
- tsconfig.base.json (shared compilerOptions + path aliases)
- tsconfig.node.json (Vite config project reference)
- scripts/build-main-ts.ts (invokes tsc for main)
- dev.ts (development coordinator; runs Vite, compiles main, launches Electron)
- build.ts (orchestration script that builds and then packages)
- jest.config.js, tsconfig.jest.json (tests; relevant to potential ESM impacts)

---

## Phase 2: ESM Migration Feasibility (Electron main)

### 1) Can the Electron main build be converted to ESM?
Yes, feasible. Electron 34 (Node 20 runtime) supports ESM in the main process. Migration requires addressing Node ESM semantics (no __dirname/require by default) and aligning packaging and dev flows.

### 2) Candidate build strategies for main/preload
- Option A: Pure tsc to ESM
  - Change tsconfig for main to output ESM (e.g., module: ESNext/NodeNext). To avoid flipping the entire repo, emit ESM into a separate directory or use .mjs extension and update package.json main accordingly.
  - Consider keeping preload compiled to CJS initially to reduce risk, then migrate later if desired.
- Option B: tsup (esbuild-based bundling)
  - Use tsup to build main (and optionally preload) with format esm, target node20, outDir build/main.
  - Pros: can inject shims (e.g., --shims gives __dirname, __filename, require via createRequire) and bundle dependencies if desired.
  - Cons: bundling can complicate native modules or dynamic requires; recommend no-bundle for native-linked modules.
- Option C: Vite (lib mode) or electron-vite
  - Possible but adds a second Vite pipeline; more tooling surface area. tsup or tsc tends to be simpler for main.

Recommendation: tsup (no-bundle) or tsc-only are both viable. tsup provides helpful ESM shims with fewer code changes; tsc provides maximal transparency but requires explicit code edits.

### 3) Compatibility check and code audit highlights
- Electron version
  - electron ^34.3.0 supports ESM in main and preload. No blocker here.

- Dependencies with ESM/CJS status
  - better-sqlite3 is CJS. In ESM, import via:
    - dynamic import: const mod = await import('better-sqlite3'); const Database = mod.default;
    - or Node's createRequire(import.meta.url) to call require('better-sqlite3').
  - Existing code (src/main/db/better-sqlite3-loader.ts, src/main/db/database-implementation.ts) uses require() intentionally; works under CJS, and under ESM with createRequire.

- Import/export patterns
  - Main code uses top-level import syntax already (TS/ES style), so module syntax largely aligns with ESM. However several files rely on CommonJS-only globals/APIs:
    - __dirname/relative file resolution:
      - src/main/main.ts: path.join(__dirname, 'docs', ...)
      - src/main/db/async-database.ts: path.join(__dirname, 'database-worker.ts') (note: references .ts; compiled artifact is .js)
    - require():
      - src/main/db/better-sqlite3-loader.ts and src/main/db/database-implementation.ts
    - These require replacing with ESM-friendly patterns: fileURLToPath(import.meta.url) for paths, createRequire for require() where unavoidable.

- Native modules and Node/Electron specifics
  - better-sqlite3 native binary loading must occur in Electron’s Node runtime (already enforced by checks). ESM itself does not break this, but bundlers must not inline/transform the native require. Prefer tsup with no-bundle or plain tsc.

- Preload script
- Now compiled to ESM (build/main/preload.mjs). Electron supports ESM preloads.

- Testing toolchain
  - Jest config is CommonJS (jest.config.js). If changing package.json to "type": "module", rename to jest.config.cjs or adopt ESM-compatible config. ts-jest can run tests that import ESM, but enabling ESM globally often requires additional config. Keeping package.json type as CommonJS and emitting main as .mjs avoids touching Jest.

- Packaging (electron-builder)
  - electron-builder is agnostic to module format. Ensure package.json "main" points to the ESM entry (e.g., build/main/main.mjs) or set "type": "module" if using .js with ESM semantics.

Overall feasibility: High, with limited targeted code changes and some build script adjustments.

---

## Phase 3: Risk Assessment

### 1) Potential regressions
- Path resolution at runtime
  - __dirname/__filename undefined under ESM; any remaining references will crash. Must migrate to fileURLToPath(import.meta.url).
- Native module loading
  - Using dynamic import vs. require for better-sqlite3 may change default/named export behavior; ensure correct access to default export and avoid bundling.
- Worker script resolution
  - Directly referencing .ts in runtime paths (e.g., database-worker.ts) will fail; must resolve to the compiled .js in build output and use ESM-safe path resolution.
- Preload handling
  - If preload migrates to ESM, verify BrowserWindow preload configuration and any assumptions around CJS globals.
- Test runner and configs
  - If package.json type is flipped to module, .js config files (jest.config.js, possibly ESLint config) must be renamed to .cjs or migrated. This can cause CI breakage if overlooked.
- Dev workflow scripts
  - dev.ts currently compiles main via tsc and starts Electron. Switching to tsup or changing output paths requires updating those commands.

### 2) Impact on functionality, packaging, and distribution
- Functionality should remain unchanged if path and loader adjustments are made correctly.
- Packaging flow remains: dist + build go into electron-builder. Only the main entry path/format changes.
- Distribution artifacts (DMG/ZIP/NSIS/AppImage, etc.) unaffected.

### 3) Breaking changes to address
- Replace all __dirname/__filename with ESM-safe equivalents.
- Replace require() in ESM files with createRequire(import.meta.url) or dynamic import and fix type usage.
- Ensure worker paths and any fs-asset lookups point to built .js and use import.meta.url resolution.
- If adopting "type": "module": rename jest.config.js → jest.config.cjs (and any other Node-loaded configs), or prefer a .mjs main entry without flipping package type.

---

## Recommended Approach (Incremental, Low-Risk)

1) Keep package.json type as CommonJS initially.
- Emit an ESM entry for main as .mjs to avoid global repo-wide ESM flip.
- Options:
  - Use tsup to emit build/main/main.mjs (format esm, no-bundle, target node20, --shims if you want __dirname/require polyfills).
  - Or use tsc with module NodeNext/ESNext and write to a separate dir (e.g., build/main-esm). If using .js outputs, you’ll need either package.json "type": "module" or .mjs extension for the entry point.

2) Address code hotspots:
- __dirname/__filename → fileURLToPath(import.meta.url) + path.dirname for any main-side files that run at runtime (e.g., src/main/main.ts open-docs code; worker paths in async-database.ts if used).
- Replace require with createRequire(import.meta.url) in ESM modules where needed (e.g., better-sqlite3 loaders), or use dynamic import and adapt types with esModuleInterop.
- Ensure worker script references resolve to .js, not .ts, in the compiled output tree.

3) Update launch/packaging references:
- package.json "main": point to build/main/main.mjs (or to the new ESM output path). Keep afterSign hooks and packaging scripts as-is (CJS).
- dev.ts: when you switch to tsup or alternative outDir, update the build command and entry path used by "npm start" (Electron reads package.json main).

4) Validate locally:
- Build renderer (Vite), build main ESM, run dev:electron and smoke test.
- Run verify-build and a test packaging (npm run package:mac|:win as applicable) to confirm electron-builder picks up the ESM entry.
- Run Jest; if you did not change package type, tests should remain unaffected. If you flip to "type": "module", rename jest.config.js → .cjs.

5) Optional later steps:
- Migrate preload to ESM when comfortable; test BrowserWindow preload behavior across platforms.
- Consider consolidating main build with tsup for faster dev iterations if desired.

---

## Appendix: File inventory reviewed

- package.json (scripts, main, electron-builder config)
- vite.config.ts (renderer)
- tsconfig.main.json (CJS → build/main)
- tsconfig.scripts.json (CJS → build/scripts)
- tsconfig.json, tsconfig.base.json, tsconfig.node.json
- tsup.config.ts, build.ts, dev.ts
- jest.config.js, tsconfig.jest.json
- Output directories: dist/, build/main/, build/scripts/
- Representative main code using CJS-only patterns:
  - src/main/main.ts (uses __dirname)
  - src/main/db/async-database.ts (uses __dirname and references .ts path)
  - src/main/db/better-sqlite3-loader.ts, src/main/db/database-implementation.ts (use require)

---

## Verdict
- Feasibility: High.
- Scope of change: Limited to main build/entry and a handful of CJS-only patterns in main-side code. Packaging and renderer remains unchanged.
- Recommended path: Emit an .mjs ESM entry for main without flipping repo-wide module type; address __dirname/require usage; validate packaging. Optionally migrate preload later.
