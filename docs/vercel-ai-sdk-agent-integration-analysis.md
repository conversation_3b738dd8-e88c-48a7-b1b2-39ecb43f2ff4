# Vercel AI SDK Agent Integration — Three-Phase Analysis

Date: 2025-09-02
Owner: Codex CLI Assistant

This document summarizes the current integration plan, highlights edit-tool capabilities we should leverage, and provides research-driven recommendations for GPT-5 and Vercel AI SDK v5. It concludes with concrete next steps to update the plan. No code changes are implemented here.

---

## Phase 1 — Review of Current Plan (`docs/vercel-ai-sdk-agent-integration.md`)

### Key Objectives
- Embed an LLM coding agent directly in PasteFlow with a dual-context workflow.
- Deliver a streamlined tool surface (file, search, edit, terminal, context) with safe, auditable operations.
- Provide a first-class agent panel UI with streaming chat, @‑mention context, and an embedded terminal for transparency.

### Proposed Architecture
- Triple-layer layout: Context (main UI) → Chat (agent panel) → Terminal (xterm.js).
- Dual-context system:
  - Initial context prepared in the main UI (bulk file selection, line ranges, prompts), “Pack → Preview → Send to Agent”.
  - Dynamic context inside the agent panel via @‑mentions and a mini file browser, with real-time token tracking.
- Consolidated tools exposed to the model (Vercel AI SDK tool-calling) under five namespaces:
  - `file`: read/write/move/delete/info/list (batch support; line ranges; token counts)
  - `search`: code/files/symbols/fuzzy (ripgrep + AST later)
  - `edit`: replace/diff/block/multi (character-level diffs; previews; multi-file)
  - `terminal`: start/interact/output/list/kill (session mgmt; ready pattern)
  - `context`: expand/search/summary (PasteFlow-specific context pipeline)
- Main-process API route (`/api/v1/chat`) uses `streamText` with tools, streams to renderer.
- Agent panel (`AgentPanel`) uses a chat hook, supports @‑mention autocomplete and inline context cards.
- Terminal Manager in main (via `node-pty`) streams to renderer; panel UI includes tabs and resizable panel.

### Implementation Approach
- Phase 1: Core chat infrastructure, chat endpoint in main process, agent panel + dynamic context, streaming UI.
- Phase 2: Dual-context transfer and system prompt composition that summarizes initial + dynamic context and entitlements.
- Phase 3: Terminal management (pty), streaming output, session lifecycle.
- Phase 4: Advanced features (code generation templates, enhanced search/symbols, richer terminal UI/UX).

Overall: Well-aligned with PasteFlow’s Electron + React architecture, clear tool boundaries, and strong UX for context-focused coding assistance.

---

## Phase 2 — Edit Tools Report Integration (`docs/codex-cli-agent-edit-tools.md`)

### Capabilities to Leverage
- Apply Patch tool:
  - Freeform grammar tool (optimized for advanced models) and JSON function variant.
  - Supports Add/Delete/Update with hunks, optional renames, and precise context headers.
- Shell tools with sandbox + approvals:
  - Default shell; sandbox-aware shell supporting `with_escalated_permissions` and `justification`.
  - Streamable shell (`exec_command`, `write_stdin`) for interactive sessions.
- Plan tool (`update_plan`): structured step tracking exposed to the UI.
- MCP extensibility: load external tools from MCP servers with deterministic ordering.

### Patterns/Details That Improve Our Plan
- Unify editing through the Apply Patch tool:
  - Map our `edit` tool’s replace/diff/block/multi semantics to a single “apply_patch” execution path (preview vs apply), preserving the consolidated UX while relying on a proven patch grammar for reliability and auditing.
- Approval-aware terminal and shell:
  - Route terminal/command execution through sandbox-aware shell primitives when agent-initiated; keep human-in-the-loop approvals for destructive commands (aligns with our security goals).
- Streamable interaction for tooling:
  - For long operations (tests/builds), prefer `exec_command` with `write_stdin` over ad-hoc IPC to simplify interactive flows and Ctrl+C behavior.
- Dynamic tool selection/config:
  - Configure tool availability by model family, approval policy, and sandbox settings; expose feature flags for write/edit/terminal.
- Visible planning:
  - Surface `update_plan` events in the agent panel to show the agent’s current steps and improve trust.

Net: Incorporate the Codex CLI tool contract directly—especially apply_patch and sandbox-aware shell—under our consolidated tool abstractions.

---

## Phase 3 — Research and Recommendations

Notes on research method: Network access to official docs is constrained by site protections. We successfully retrieved the Vercel AI SDK README (ai package) from GitHub and observed the current API surface; however, we could not fetch Cloudflare‑protected OpenAI docs pages. Recommendations below are grounded in the AI SDK README and our existing plan, and framed to remain model/provider‑agnostic where GPT‑5 specifics are unavailable.

### Findings: Vercel AI SDK v5 (and current ai-sdk.dev)
- Package layout and usage:
  - Core: `ai` provides `generateText`, `streamText`, provider integrations like `@ai-sdk/openai`.
  - UI hooks: `@ai-sdk/react` provides `useChat` with a UI‑message structure (messages split into `parts`, e.g., text/tool parts).
  - Server streaming helpers return UI-oriented streams (e.g., `toUIMessageStreamResponse()` in README), which the React hooks can consume directly.
- Tool calling:
  - The SDK supports model tool calling via `tools: { name: tool({ description, parameters: zod, execute }) }` passed to `streamText`, which fits our consolidated tool design.

Implications for our code:
- Prefer `@ai-sdk/react` over legacy `ai/react` imports in the renderer.
- Align server responses with UI message stream: use `toUIMessageStreamResponse()` instead of custom transports.
- Adopt the UI message schema (`messages[].parts[]`) in the agent panel to stay forward-compatible with the SDK.

### Findings: GPT‑5 Capabilities (constraints)
- Direct GPT‑5 docs retrieval was blocked (Cloudflare). The integration must not hardcode GPT‑5 specifics.
- Architectural posture:
  - Keep model selection provider‑agnostic; support `openai('gpt-5')` if available, but default to well-known models (e.g., `gpt-4o`) without breaking.
  - Design tools with robust schemas (Zod) and explicit previews; assume improving tool reliability but do not depend on model‑specific behaviors.
  - Plan for large context windows and higher tool‑calling cadence: enforce server-side token/size limits and tool budgets regardless of model.

### Recommendations

1) Edit Tool Concepts To Incorporate
- Standardize edits on the Apply Patch grammar:
  - Keep the user‑facing `edit` tool API (replace/diff/block/multi) but implement it using a single `apply_patch` path that produces and applies diffs with previews.
  - Return structured results: preview/applied state, character-level diffs, and file paths for renderer diffs.
- Integrate sandbox-aware shell and streamable exec:
  - For agent‑initiated commands, use a shell tool that respects `with_escalated_permissions` and `justification` (mirrors our approvals UX).
  - For interactive runs (dev server/tests), use `exec_command` + `write_stdin` instead of raw PTY when appropriate; keep xterm.js for display and user input.
- Surface `update_plan` inside the agent UI:
  - Render a minimal plan panel showing the agent’s current steps, mapped from tool calls to user‑readable items.
- MCP-ready tool layer:
  - Define a thin adapter to register optional MCP tools in our consolidated tool map; consistently name tools for deterministic ordering.

2) GPT‑5 Influence on Architecture
- Provider‑agnostic model layer:
  - Wrap model selection in a small adapter that normalizes capabilities (tool calling, JSON output modes) and fallbacks (gpt‑4o, etc.).
- Token governance first:
  - Enforce caps for initial and dynamic context regardless of model; expose thresholds in the UI and block sends when above budget.
- Deterministic tool schemas:
  - Use Zod schemas with strict typing and small payloads for tool args; require previews for writes and produce auditable logs.
- Streaming and backpressure:
  - Ensure `execute` paths for tools are non‑blocking and chunk results; if output is large, return a handle and let the UI fetch paged results.

3) Vercel AI SDK v5 Features To Exploit
- `@ai-sdk/react` hooks and UI message schema:
  - Switch to `@ai-sdk/react` `useChat`, update message handling to use `parts`, and return `toUIMessageStreamResponse()` from the server route.
- Tool calling in `streamText`:
  - Keep our consolidated `tools` map; ensure parameters use Zod and outputs are compact and serializable for the UI.
- Provider modules:
  - Use `@ai-sdk/openai` and pass model IDs dynamically from a ModelSelector in the agent header.

4) Concrete Next Steps To Update the Plan
- API/Server
  - Replace `result.toDataStreamResponse()` with `result.toUIMessageStreamResponse()` in `/api/v1/chat`.
  - Normalize messages to the UI‑message format; ensure `messages` and tool calls adhere to SDK v5 expectations.
  - Implement consolidated tools as thin wrappers over existing main-process services; add a dedicated `src/main/agent-tools/` module.
  - Wire all file and search operations through existing PathValidator; add allowlist/denylist and redaction before model streaming.
- Renderer
  - Migrate from `ai/react` to `@ai-sdk/react`; update `AgentPanel` to the new `useChat` message shape with `parts`.
  - Remove or replace `DataStreamChatTransport` with the built-in `useChat` fetch workflow.
  - Add an “Agent Plan” panel fed by `update_plan` events.
  - Implement @‑mention parsing with line-range support and guarded folder/glob expansion.
- Terminal/Execution
  - Start with a view‑only terminal; add “Share last output to agent” control.
  - Introduce feature flags: `agent.allowFileWrite`, `agent.allowTerminal`, `agent.shareTerminalToModel`, `agent.maxTokens`.
  - When enabling execution, prefer streamable exec tools for interactive flows and approvals for destructive actions.
- Security/Observability
  - Enforce token/size caps for initial and dynamic context; block sends over limits.
  - Add an audit log table for tool executions (args, preview/applied, bytes, duration, approvals).
  - Add redaction rules (e.g., .env patterns, keys) with opt‑in bypass per session (logged).
  - Count tokens and tool calls per turn; surface usage in the UI and store summaries.

---

## File References
- docs/vercel-ai-sdk-agent-integration.md
- docs/codex-cli-agent-edit-tools.md

## Open Questions / Follow-ups
- Confirm GPT‑5 model ID and any model‑specific constraints once official docs are accessible.
- Verify whether `DataStreamChatTransport` exists in the targeted SDK; otherwise, proceed with the UI message stream helpers.
- Decide on ESM vs CJS handling in Electron main; either convert to ESM or dynamically import SDK modules.

## Appendix — Minimal Server/Client Alignment Checklist
- Server route uses `streamText(...)` and returns `toUIMessageStreamResponse()`.
- Renderer uses `@ai-sdk/react` `useChat` and expects `messages[].parts[]`.
- Tools use Zod schemas, preview mode for writes, and small return payloads; large results are chunked or retrieved via follow-up calls.
- All file/search ops validated via PathValidator with allowlist/denylist; redaction before model send.
- Feature flags gate write/terminal; approvals required for destructive actions.

