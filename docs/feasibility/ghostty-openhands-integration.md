## Feasibility Assessment: Integrating Ghostty and OpenHands into PasteFlow (Electron/React/TS)

### Executive Summary
- Ghostty: Direct embedding inside an Electron/React renderer is currently not feasible. Ghostty is a standalone native terminal emulator (Zig/Cocoa/GTK) and does not expose an embeddable widget or stable library surface for Electron. Recommended approach: keep <PERSON>ty as an external companion terminal (launched and orchestrated from PasteFlow), or use an in-app terminal via node-pty + xterm.js if embedded terminal UX is required.
- OpenHands CLI Agent: Feasible via a process boundary. Best approach is to run it as a managed child process (Python venv) or localhost service and integrate via a typed IPC bridge (stdio/JSON or HTTP/WebSocket) confined to the active workspace paths. Expect notable integration and packaging work, especially for cross-platform and model backends.

Outcome: Proceed with (A) xterm.js-based embedded terminal for in-app needs + (B) optional “Open in Ghostty” external workflow; and (C) a forked OpenHands adapter exposing a minimal JSON-RPC surfaced through our Electron IPC with strict path validation, audit logging, and resource limits.

---

### What We Found in PasteFlow Codebase
- Main process IPC and schemas
  - src/main/main.ts defines many ipcMain.on/handle routes with zod validation from src/main/ipc/schemas.ts.
  - Event streaming patterns are present (e.g., request-file-list → file-list-data, file-processing-status) and request/response handles (e.g., /workspace/*, request-file-content).
- Security model
  - Path scoping/validation: src/security/path-validator.ts uses allowed workspace paths and denies traversal/out-of-scope access. Workspace selection updates allowed paths (main.ts setsAllowedWorkspacePaths + getPathValidator).
  - Renderer uses window.electron.ipcRenderer (preload-isolated bridge implied), suggesting contextIsolation on and explicit IPC channels.
- File ops
  - Filters and binary detection via src/file-ops/filters; binaryExtensions in src/shared/excluded-files. Existing utilities can be re-used to constrain agent operations.
- Renderer IPC setup
  - src/handlers/electron-handlers.ts registers listeners for folder-selected, file-list-data, file-processing-status and exposes helpers to manage a long-running streaming request (global requestId handling). Good precedent for agent log/stream events.

Implication: PasteFlow already has the primitives we need to add secure IPC routes, stream logs/progress, validate paths, and persist or audit operations.

---

### Ghostty Integration Analysis
- Embedding feasibility
  - Ghostty is a native terminal app (Zig) targeting platform UI stacks (Cocoa/Metal on macOS, GTK on Linux). There is no documented embeddable component for web/Electron renderers.
  - Electron renderers are web-based; hosting a native NSView/GTK widget requires a custom native node addon that bridges to BrowserWindow, and would still need Ghostty to expose an SDK/view. Today, such an API is not public and would be brittle.
- Recommended approaches
  1) External companion terminal (recommended if Ghostty UX is desired):
     - Launch Ghostty via child_process with cwd/profile arguments to open in the current workspace. Provide deep-link actions (e.g., open a specific file, run a command, set env) if Ghostty supports args or URL scheme.
     - Offer an “Open in Ghostty” button and synchronize selections (e.g., launching with working directory = selected workspace or file path).
     - Capture only high-level status (process spawn success, exit code); cannot embed Ghostty UI.
  2) Embedded terminal inside PasteFlow:
     - Use node-pty in main/preload to spawn a shell confined to the workspace and xterm.js in the renderer to display and interact. This provides full in-app terminal UX and IPC streaming using patterns already present.
     - Optionally, theme/UX can be made “Ghostty-like.”
- Cross-platform
  - Ghostty availability is strong on macOS and Linux; Windows support is evolving and may be incomplete. Relying on Ghostty as an external hard dependency risks breaking Windows support.
  - xterm.js/node-pty supports macOS/Linux/Windows broadly and is commonly used in Electron apps (VS Code precedent).
- Build/packaging
  - Shipping Ghostty binaries inside PasteFlow is not recommended (licensing, updates, notarization, code signing). Prefer discovering an existing Ghostty installation (PATH lookup) and offering guidance if not installed.
  - xterm.js adds only JS/TS deps and a native node-pty binary build; compatible with typical Electron build chains.
- Security
  - External Ghostty runs out-of-process; PasteFlow can limit only what it passes as cwd/env. No control over file access beyond OS-level permissions.
  - Embedded node-pty terminal should be sandboxed to the workspace: enforce cwd within allowed paths; gate any programmatic file operations via our IPC and path-validator; restrict environment variables/tokens.
- Performance
  - External Ghostty: negligible impact on PasteFlow process; GPU rendering and performance are Ghostty’s concern.
  - xterm.js/node-pty: proven performant; ensure backpressure handling over IPC and detach large scrollback to avoid memory bloat.

Conclusion: Direct embedding of Ghostty is not feasible. Implement an embedded terminal via node-pty + xterm.js, plus an optional “Open in Ghostty” command for users who have it installed.

---

### OpenHands Integration Analysis
- Architecture options
  1) Managed child process (stdio JSON-RPC) [preferred]:
     - Spawn the OpenHands CLI/entrypoint in a Python virtualenv with arguments to set the workspace directory and model/backend settings.
     - Speak a minimal JSON-RPC over stdin/stdout (or a line-delimited JSON protocol). The agent reports events (state updates, logs, tool calls), and PasteFlow sends high-level commands (start/stop, apply patch, read file, tool invocation authorization).
  2) Local HTTP/WebSocket service:
     - Start OpenHands in server mode on an ephemeral localhost port with a random auth token.
     - Renderer communicates only via Electron main (ipcMain.handle routes) which proxy to the local service; never expose the service directly to the renderer.
- Required modifications to OpenHands (fork/customization)
  - Filesystem adapter interface:
    - Replace raw FS access with an abstracted “workspace FS” module that enforces allowed path rules and supports read/write/rename/delete with audit hooks.
    - Add optional “dry-run” and “approval required for destructive ops” flags configurable by PasteFlow.
  - Clipboard/tools integration:
    - Introduce a “tool” in OpenHands that delegates clipboard set/get to PasteFlow via RPC (Electron main handles clipboard using Electron.clipboard securely in main process).
  - Logging and event stream:
    - Ensure the agent emits structured events (JSON) for token usage, model calls, tool invocations, diffs/patches, and errors.
    - Add correlation IDs for steps to support UI progress and replay.
  - Auth/Secrets handling:
    - Remove the need for agent to read environment variables/files directly; pass model keys via process env on spawn or one-time IPC handshake; avoid printing secrets.
  - Concurrency/limits:
    - Add flags for max parallel tool calls, rate limits, and max file size to read.
- Compatibility with PasteFlow stack
  - Electron/TypeScript: Communicate through ipcMain and typed zod schemas; Renderer never talks to Python directly.
  - SQLite: Use existing database bridge to persist agent runs, prompts, audit logs; extend schemas minimally.
  - File ops: Reuse src/file-ops utilities and path-validator for enforcement. Gate all agent file operations behind main-process validators.
- Security considerations
  - Strict workspace confinement using PathValidator for every file touch (both agent initiated and PasteFlow-initiated).
  - Run the Python child process with minimal environment; optionally chroot/jail where available (Linux bubblewrap, macOS sandbox-exec, Windows job object/SRP) for added isolation.
  - No inbound listening sockets unless bound to localhost with a random token; prefer stdio.
  - Red-team the JSON protocol to avoid command injection, ensure schema validation on both sides.
- Performance
  - Python agent process overhead is acceptable; main costs are LLM calls and file scanning. Use streaming transport and incremental diffs.
  - Ensure backpressure in IPC to avoid renderer freezes; keep heavy work in main/OpenHands processes.
- Cross-platform
  - Python is cross-platform; venv creation and packaging are the biggest friction. Some OpenHands features may rely on Docker; provide a “no-Docker” path or detect and disable gracefully on Windows/macOS without Docker.

---

### IPC Design Recommendations
- New main-process channels (examples; validate with zod):
  - /agent/start { workspaceId, params }
  - /agent/stop { agentId }
  - /agent/command { agentId, action, payload }
  - Events: agent-log, agent-state, agent-error, agent-result (streamed similarly to file-list-data)
- Terminal integration channels:
  - /terminal/start { cwd, shell, env }
  - /terminal/write { sessionId, data }
  - /terminal/resize { sessionId, cols, rows }
  - Events: terminal-data, terminal-exit
- Security gating
  - All file requests from OpenHands and terminal sessions run with cwd within allowed workspace; validate every path via PathValidator.
  - Add per-workspace capability tokens to correlate events and prevent cross-workspace leakage.

---

### Recommended Architecture
- Renderer (React/TS)
  - UI components: Embedded terminal (xterm.js), Agent run panel (logs, approvals, diffs), “Open in Ghostty” action.
  - Uses window.electron.ipcRenderer to talk to main; never direct process or FS access.
- Main (Electron)
  - IPC router with zod schemas (extend src/main/ipc/schemas.ts) for agent and terminal.
  - Terminal service: node-pty lifecycle, stream bridge, workspace confinement.
  - Agent service: spawn and supervise Python process; stdio JSON-RPC; enforce path validation on every FS action; persist audit/logs in SQLite.
  - Clipboard bridge exposed as an agent tool, gated via main (no renderer direct clipboard writes by agent).
- OpenHands fork/module
  - FS adapter, clipboard tool, structured logs, approval hooks, config surface for limits.

---

### Build System & Packaging
- Ghostty
  - Do not bundle Ghostty. Detect availability on PATH (ghostty --version). If absent, show CTA to install with platform-specific instructions. Keep it optional.
- Terminal (embedded)
  - Add node-pty and xterm.js deps; ensure native rebuild for Electron ABI (prebuildify/electron-builder handles). Include minimal theming.
- OpenHands
  - Ship no Python in app by default. On first use, bootstrap a dedicated per-version venv under app data and pip install openhands + pinned deps. Cache and check integrity.
  - Alternatively, support user-provided Python path and venv. Maintain a compatibility matrix in docs.
- CI/CD
  - Add smoke tests to spawn terminal and agent in CI (no Docker). Run lint/typecheck/build pre-push as per team workflow.

---

### Security Model Enhancements
- Extend PathValidator to include per-operation policy (max file size, disallowed globs, rate limits).
- Add allowlist of agent tools; require user approval for write/delete/exec operations (similar to VS Code “apply changes” prompts).
- Sanitize logs for secrets; redact environment variables; configurable telemetry off by default.
- If running HTTP mode for agent, bind to 127.0.0.1 with random token; short TTL; never expose to renderer directly.

---

### Estimated Effort (Rough)
- Embedded terminal (node-pty + xterm.js): 3–5 days (MVP: single session, resize, basic theme, scrollback; plus tests and docs).
- “Open in Ghostty” external integration: 0.5–1 day (spawn + args + detection + UX; platform nuances may add 0.5 day).
- OpenHands fork + adapter: 2–3 weeks
  - JSON-RPC protocol + supervisor: 3–4 days
  - FS/clipboard adapters + approval hooks + tests: 4–6 days
  - Packaging bootstrap (venv mgmt, pinning, integrity): 3–5 days
  - UI (logs, diffs, approvals, run history): 4–6 days
- Hardening & cross-platform polish: +1–2 weeks (Windows nuances, sandboxing options, performance tuning).

---

### Risks & Mitigations
- Ghostty non-embeddable: Mitigate by external launch + in-app terminal alternative.
- Python packaging fragility: Use per-version venv, hash-locked requirements, health checks, and self-repair.
- Security of an autonomous agent: Strict workspace confinement, approvals, logging, and optional “dry-run” mode.
- Windows support: Avoid Ghostty dependency; ensure node-pty backend works on conpty; test OpenHands without Docker.

---

### Alternative Approaches
- Terminal: Stay fully in-app with xterm.js and provide theme/profile support. Defer Ghostty integration to an “open externally” convenience only.
- Agent: Instead of OpenHands, consider lighter agents with clear stdio protocols (e.g., Continue server, or a bespoke agent) if OpenHands proves heavy. Or run OpenHands in a container where available (user opt-in) to improve isolation.

---

### Next Steps
1) Approve architecture: xterm.js embedded terminal + optional external Ghostty launcher; OpenHands via stdio JSON-RPC child process.
2) Spike: small POC for node-pty/xterm.js session with our IPC pattern; spike a Python echo agent over stdio through main to validate transport.
3) Define JSON schemas in src/main/ipc/schemas.ts for terminal and agent routes; add typed client helpers in renderer.
4) Draft OpenHands fork plan (FS adapter, tool interface), and venv bootstrapper design.
5) Security review: policies for approvals, allowed paths, size limits, and logging redaction.

