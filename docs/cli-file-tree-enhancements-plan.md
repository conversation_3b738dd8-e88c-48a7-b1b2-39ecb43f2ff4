# PasteFlow CLI Enhancements: File Tree Mode + Status Summaries + ASCII Tree

## Overview

This plan describes how to extend the PasteFlow CLI to:

- Expose the current file tree mode in `pf select list` and `pf status`.
- Enhance `pf status` with selection counts and token totals, and an optional verbose listing of selected files with tokens.
- Add a new `pf tree` command to output an ASCII file tree based on the active file tree mode.

Goals:

- Maintain backward compatibility and follow existing CLI patterns (flags, `--json`, error handling).
- Reuse existing backend APIs where possible; add a focused new API to enable ASCII tree output.
- Integrate cleanly with the workspace state model and selection logic already in PasteFlow.

## Current State (Key Findings)

- CLI entrypoint and commands live under `cli/src/`.
  - CLI root: `cli/src/index.ts:1` attaches commands and defines global flags (`--json`, `--debug`, etc.).
  - `pf select list` implementation: `cli/src/commands/select.ts:133` calls `GET /api/v1/selection/tokens` and prints a table or totals.
  - `pf status` implementation: `cli/src/commands/status.ts:1` currently shows server status, active workspace, and allowed paths only.

- Backend API (Electron main) routes and helpers:
  - Route wiring: `src/main/api-server.ts:96` (registerRoutes) and handlers in `src/main/api-route-handlers.ts`.
  - Status route: `src/main/api-route-handlers.ts:41` → `handleStatus` returns `status`, `activeWorkspace` (id, name, folderPath), and `securityContext.allowedPaths`.
  - Selection endpoints (used by CLI today):
    - `POST /api/v1/files/select` / `POST /api/v1/files/deselect` / `POST /api/v1/files/clear`
    - `GET /api/v1/files/selected` returns current selection (absolute paths + optional line ranges) [`src/main/api-server.ts:200`].
    - `GET /api/v1/selection/tokens` returns per-file tokenization and totals [`src/main/api-server.ts:253`].
  - Content aggregation (for preview/content APIs) loads and formats selected files, and injects file tree header when enabled:
    - `src/main/content-aggregation.ts` — builds the file list scoped by file tree mode; uses `getSelectedFilesContent`.
    - `src/utils/content-formatter.ts:165` — generates `<file_map>` header and uses `generateAsciiFileTree`.
  - The ASCII tree generator lives at `src/file-ops/ascii-tree.ts`.
  - File tree mode is part of workspace state and is read in `parseContentOptions`:
    - `src/main/api-server.ts:590` enforces valid modes and defaults.
    - Workspace state default includes `fileTreeMode: 'selected-with-roots'` in `src/main/db/database-implementation.ts:314`.

Takeaway: The server already knows the active `fileTreeMode` via workspace state. There’s a tokenization endpoint we can reuse for counts and per-file tokens. No endpoint exists to return only the ASCII tree; that requires a new minimal API.

## Requirements Mapping → Plan

1) Enhance `pf select list` to include the current file tree mode.
   - Fetch active workspace → fetch workspace state → read `fileTreeMode`.
   - Keep current output; add a line like `File Tree Mode: <mode>`.
   - For `--json`, include `fileTreeMode` field alongside existing payload to preserve compatibility.

2) Enhance `pf status` with:
   - Selected Files: count of selected files (number of items in `GET /api/v1/files/selected`).
   - Tokens (Selected Files): use `GET /api/v1/selection/tokens` and display `totals.files`.
   - Optional `--include-selection` to print a table of selected files with individual token counts (reusing the same tokens payload). For `--json`, include a `selection` block only when the flag is present.

3) Enhance `pf status` to display file tree mode.
   - Same approach as (1): fetch active workspace id via `/api/v1/status`, then `GET /api/v1/workspaces/:id` to read `state.fileTreeMode`.
   - Optional small backend improvement: include `fileTreeMode` in `/api/v1/status` for a single-call path (not required for this milestone; see Backend Changes below).

4) New CLI `pf tree` to output ASCII representation of the current file tree.
   - Add `GET /api/v1/tree` endpoint that computes the ASCII tree given current selection and file tree mode, returning `{ mode, root, tree }`.
   - CLI calls this endpoint and prints the tree, or outputs JSON when `--json` is set.

## Detailed Design

### CLI Changes

1. `pf select list` (file: `cli/src/commands/select.ts`)
   - Today: calls `GET /api/v1/selection/tokens` and prints files/prompts/instructions + totals.
   - Add: fetch current `fileTreeMode`.
     - Call `GET /api/v1/status` to get `activeWorkspace.id`.
     - If present, call `GET /api/v1/workspaces/:id` and read `data.state.fileTreeMode`.
     - If absent or invalid, default to `'selected'` (same default used in server).
   - Output:
     - Text: print a new line near the end (just before totals) e.g., `File Tree Mode: selected-with-roots`.
     - JSON: wrap the existing payload in an object that also includes `fileTreeMode` or, more conservatively, add a sibling field `fileTreeMode` at the top level when printing (preserves existing consumer parsing of `files`, `prompts`, `totals`).
   - Flags: preserve current flags.
   - Errors: if the workspace/state fetch fails with `NO_ACTIVE_WORKSPACE`, still print selection info (if any). Show `File Tree Mode: n/a` or omit. Maintain current exit codes from `handleAxiosError` for the primary call.

2. `pf status` (file: `cli/src/commands/status.ts`)
   - Today: prints server status, active workspace, and allowed paths based on `GET /api/v1/status`.
   - Add:
     - Selected Files count: `GET /api/v1/files/selected` length (gracefully handle `NO_ACTIVE_WORKSPACE` by showing `0`).
     - Tokens (Selected Files): `GET /api/v1/selection/tokens` → use `totals.files` (gracefully handle errors, show `0`).
     - File Tree Mode: resolve via `status` → `workspaces/:id` state read.
     - New flag `--include-selection` (alias `--verbose-selection` if you prefer both): when present, print a compact table with per-file token counts. Reuse the table formatting from `select list` (columns: Type, Path/Name, Ranges, Tokens, Note) but include only File rows for brevity.
   - JSON output: include extra fields in a `selectionSummary` object:
     ```json
     {
       "status": "running",
       "activeWorkspace": { ... },
       "securityContext": { ... },
       "fileTreeMode": "selected-with-roots",
       "selectionSummary": {
         "selectedFiles": 5,
         "fileTokens": 12345,
         "files": [ ... ] // only when --include-selection
       }
     }
     ```
     Keep existing fields to avoid breaking scripts.
   - Error handling: `status` should still succeed and print server details even if selection-related calls fail (log mapped error to stderr, continue with zeros unless `--json`, in which case include an `error` block for the selection sub-call if present).

3. New `pf tree` command (new file: `cli/src/commands/tree.ts`)
   - Command: `pasteflow tree` (alias `pf tree`).
   - Description: `Output the ASCII file tree for the active workspace selection and file tree mode`.
   - Behavior:
     - Calls `GET /api/v1/tree`.
     - Text mode: print `Root: <root>`, `Mode: <mode>`, blank line, then the ASCII tree. If mode is `none` or no files, print a helpful message (server provides `tree` string — often `"No files selected."`).
     - JSON mode: print `{ mode: string, root: string, tree: string }`.
   - Flags: keep standard global flags; no extra flags needed initially. Optionally, we can add `--mode <override>` later.
   - Error handling: standard `handleAxiosError` mapping.

4. CLI wiring
   - Attach the new command in `cli/src/index.ts:26` (after other `attach*Command` calls): `attachTreeCommand(program)`.

### Backend Changes

1. New `GET /api/v1/tree` endpoint
   - Add route in `src/main/api-server.ts` within `registerRoutes()`:
     ```ts
     this.app.get('/api/v1/tree', (req, res) => this.handleFileTree(req, res));
     ```
   - Implement `handleFileTree(req, res)` using the same state plumbing as content aggregation:
     - Validate active workspace (`validateActiveWorkspace`).
     - Use `parseContentOptions(req, validation)` to read:
       - `folderPath`, `selection`, `fileTreeMode`, `selectedFolder`, `exclusionPatterns`.
     - Build the tree items for the current mode:
       - If `fileTreeMode === 'none'`: return `{ mode: 'none', root, tree: '' }` (empty string acceptable).
       - If `fileTreeMode === 'selected'`:
         - Items = pruned selected files: `{ path: s.path, isFile: true }` for each selected entry.
       - If `fileTreeMode === 'selected-with-roots'`:
         - Compute all parent directories of selected files relative to `root` using `getAllDirectories` from `src/file-ops/path.ts`.
         - Items = `[...directories.map(d => ({ path: d, isFile: false })), ...selected files as isFile: true]`.
       - If `fileTreeMode === 'complete'`:
         - Scan the workspace (gitignore + exclusion patterns) and include all non-skipped files.
         - We can reuse the existing BFS implementation in `buildAllFiles` (currently internal to `src/main/content-aggregation.ts`). Options:
           - Option A (preferred): export a new helper (e.g., `scanAllFilesForTree`) from `content-aggregation.ts` that returns minimal file list for tree rendering. Reuse the same ignore logic and batching.
           - Option B: duplicate the minimal scanning logic inside `api-server.ts` (not ideal; small duplication acceptable if refactor adds complexity).
     - Determine `root` as `selectedFolder || folderPath` (same rule as `content-aggregation.ts:248`).
     - Call `generateAsciiFileTree(items, normalizedRoot)` from `src/file-ops/ascii-tree.ts`.
     - Return JSON: `{ mode: fileTreeMode, root: rootFolder, tree: ascii }`.
     - Errors: `NO_ACTIVE_WORKSPACE` (400), `PATH_DENIED` (403), and 500 DB/FS errors via `toApiError`, mirroring existing patterns.

2. Optional: Enrich `GET /api/v1/status`
   - To reduce client hops, we can include `fileTreeMode` as a top-level field in the status payload when an active workspace exists (read from the same `ws.state`). This is not strictly required because the CLI can resolve it with an extra `GET /api/v1/workspaces/:id` call; include only if low-risk.

### Data Contracts / JSON Shapes

- `GET /api/v1/tree` Response (200):
  ```json
  {
    "mode": "selected-with-roots",
    "root": "/abs/workspace/root",
    "tree": "└── src\n    ├── main.ts\n    └── ...\n"
  }
  ```

- `pf status --json` (extended): includes `fileTreeMode` and `selectionSummary`:
  ```json
  {
    "status": "running",
    "activeWorkspace": { "id": "...", "name": "...", "folderPath": "..." },
    "securityContext": { "allowedPaths": ["..."] },
    "fileTreeMode": "selected-with-roots",
    "selectionSummary": {
      "selectedFiles": 5,
      "fileTokens": 12345,
      "files": [
        { "path": "...", "ranges": null, "tokenCount": 321, "partial": false, "skipped": false }
      ]
    }
  }
  ```

- `pf select list --json` (extended): existing payload plus `fileTreeMode` field at the top level (non-breaking for consumers that access `files`, `prompts`, `totals`).

## Step-by-Step Implementation Plan

1) Backend: Add `GET /api/v1/tree`
   - Update `src/main/api-server.ts`:
     - Register route in `registerRoutes()`.
     - Implement `private async handleFileTree(req, res)`:
       - Call `validateActiveWorkspace`.
       - Call `parseContentOptions` to get `folderPath`, `selection`, `fileTreeMode`, `selectedFolder`, `exclusionPatterns`.
       - Build `items` depending on `fileTreeMode`:
         - For `selected`: `selection.map(s => ({ path: s.path, isFile: true }))`.
         - For `selected-with-roots`: use `getAllDirectories(selection, root)` to compute directories, then merge with selected file items.
         - For `complete`: reuse scanning logic (Option A preferred): expose a helper from `content-aggregation.ts` to get all files (non-skipped) for the workspace, then map to `{ path, isFile: true }`.
       - `const root = selectedFolder || folderPath;`
       - `const tree = generateAsciiFileTree(items, root);`
       - `return res.json(ok({ mode: fileTreeMode, root, tree }));`
   - Add any small exports required (e.g., export a scanning helper) in `src/main/content-aggregation.ts` to avoid duplication.
   - Error mapping: use `toApiError` helpers and return codes consistent with existing endpoints (see `src/main/api-route-handlers.ts:274` et al.).

2) CLI: Enhance `pf select list`
   - File: `cli/src/commands/select.ts`:
     - After fetching tokens, fetch active workspace file tree mode:
       - `GET /api/v1/status` → if `activeWorkspace`, fetch `GET /api/v1/workspaces/:id` and read `state.fileTreeMode`.
       - If the extra call fails, default to `'selected'` and proceed.
     - Text mode: append `File Tree Mode: <mode>` before totals.
     - JSON mode: include `fileTreeMode` at the top level (e.g., `{ ...data, fileTreeMode }`).
     - Preserve existing flags and error handling via `handleAxiosError`.

3) CLI: Enhance `pf status`
   - File: `cli/src/commands/status.ts`:
     - Add options: `.option("--include-selection", "Show selected files with token counts", false)`.
     - In the action handler:
       - Make the existing `GET /api/v1/status` call first.
       - Resolve `fileTreeMode` via `GET /api/v1/workspaces/:id` on best-effort basis (skip on error and set to `n/a`).
       - Best-effort calls:
         - `GET /api/v1/files/selected` → count = `length` (0 on `NO_ACTIVE_WORKSPACE`).
         - `GET /api/v1/selection/tokens` → tokens = `totals.files`; also keep `files[]` if `--include-selection`.
       - Text mode:
         - Keep current lines (Server, Active Workspace, Allowed Paths).
         - Add:
           - `File Tree Mode: <mode>`
           - `Selected Files: <count>`
           - `Tokens (Selected Files): <totals.files>`
         - If `--include-selection`, print a compact table with just the file rows (similar to `select list`).
       - JSON mode:
         - Add `fileTreeMode` and `selectionSummary` as documented above (omit `files` array unless `--include-selection`).
       - Error handling:
         - If selection/token calls fail, still print status; in JSON include an `error` block under `selectionSummary`.

4) CLI: New `pf tree`
   - New file: `cli/src/commands/tree.ts`:
     - Commander shape:
       ```ts
       export function attachTreeCommand(root: any): void {
         root.command("tree")
           .description("Output the ASCII file tree for the active workspace and mode")
           .action(async () => { /* call GET /api/v1/tree, print */ });
       }
       ```
     - Text: print `Root:` and `Mode:` headers, then the ascii tree string as-is.
     - JSON: `printJsonOrText({ mode, root, tree }, flags)`.
     - Use `discover`, `createClient`, and `handleAxiosError` patterns from other commands.
   - Wire in `cli/src/index.ts`: call `attachTreeCommand(program)` below other attachments.

5) Docs
   - Update CLI usage in `README.md`:
     - `pasteflow select list` → mention it now prints `File Tree Mode` and supports the same options.
     - `pasteflow status [--include-selection]` → document the new summary fields and the optional verbose output.
     - New `pasteflow tree` → describe purpose, sample output, and `--json` support.

## Edge Cases & Error Handling

- No active workspace:
  - `pf status`: still prints server details; selection count/tokens fall back to `0`. JSON includes an `error` code for those subcalls when applicable.
  - `pf select list`: `fileTreeMode` resolution may fail; omit or display `n/a` while keeping the tokens table (if available).
  - `pf tree`: server returns `NO_ACTIVE_WORKSPACE` (400) → map to exit code 2 (validation) via `handleAxiosError`.

- `fileTreeMode === 'none'`:
  - `pf tree`: return empty `tree` string; CLI prints headers and a helpful message if `tree` is empty (e.g., "File tree mode is 'none'.").

- Selection is empty:
  - `pf tree`: returns `tree: "No files selected."` from the generator, which is fine to print.

- Large workspaces with `complete` mode:
  - Reuse the existing BFS scanner + ignore rules from `content-aggregation.ts` to avoid new performance pitfalls.
  - Consider yielding control/batching (as the existing scanner does) to keep UI responsive. As this runs server-side, we rely on the same implementation.

- Binary files and special files:
  - Tree items include file names only; binary handling does not affect tree shape.

## Testing Strategy

1) API tests (`src/main/__tests__`):
   - Add new tests for `GET /api/v1/tree` covering:
     - No active workspace → 400 `NO_ACTIVE_WORKSPACE`.
     - Workspace with 0 selected files, mode `selected` → returns `"No files selected."`.
     - Mode `selected-with-roots`: directories appear as expected for a couple of nested files.
     - Mode `complete`: tree includes all files under workspace honoring ignore rules.
   - Optionally, extend an existing end-to-end test (e.g., `api-server-phase3.test.ts`) to hit `/api/v1/tree` after selection.

2) CLI smoke tests (optional):
   - Add minimal Jest tests that mock Axios to validate that:
     - `pf status` includes new lines and JSON fields.
     - `pf select list` emits `fileTreeMode` in JSON.
     - `pf tree` prints the ASCII string from the mocked endpoint.
   - If CLI testing infra isn’t present, rely on manual verification steps below and prioritize API tests.

3) Manual verification checklist:
   - Start app (`npm run dev:electron`), open a workspace via UI or `pf folders open --folder <path>`.
   - Select a few files and switch file tree modes in the UI.
   - Run:
     - `pf select list` → confirm `File Tree Mode: <mode>` line.
     - `pf status` → confirm `File Tree Mode`, `Selected Files`, and `Tokens (Selected Files)`; add `--include-selection` to see the table.
     - `pf tree` → confirm ASCII tree matches the UI’s mode.
   - Use `--json` variants and verify shapes.

## Work Notes & Code References

- CLI files to modify/add:
  - `cli/src/commands/select.ts:133` (enhance list output and JSON to include `fileTreeMode`).
  - `cli/src/commands/status.ts:1` (augment output, add `--include-selection`, resolve `fileTreeMode`).
  - `cli/src/commands/tree.ts` (new) and `cli/src/index.ts:26` (attach).

- Server changes:
  - `src/main/api-server.ts:96` (register new `/api/v1/tree` route) and `handleFileTree` implementation.
  - Optionally: enrich `src/main/api-route-handlers.ts:41` `handleStatus` to include `fileTreeMode`.
  - For scanning in `complete` mode, prefer extracting a reusable helper from `src/main/content-aggregation.ts` (currently builds the all-files list honoring ignore rules).

## Rollout & Backward Compatibility

- Existing commands and flags keep their behavior; new fields are additive in JSON, and extra text lines are appended.
- New `pf tree` command is independent and safe to add.
- No database migrations required.

## Follow-Ups (post-milestone, optional)

- Add a dedicated CLI `pf config` subcommand to view/change workspace settings such as `fileTreeMode` (via `PUT /api/v1/workspaces/:id`), bringing parity with the UI toggle.
- Extend `/api/v1/tree` with optional `?mode=<override>` query param if users want to preview a specific mode without changing workspace state.
- Performance benchmarking on very large repos when using `complete` mode; consider caching or limiting depth.

