# LLM Coding Agent Integration Plan for PasteFlow

```
╔═══════════════════════════════════════════════════════════════════════╗
║                                                                       ║
║   ┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐         ║
║   │ Context │───▶│  Agent  │───▶│  Tools  │───▶│ Actions │         ║
║   │ Builder │    │   Chat  │    │   API   │    │  Engine │         ║
║   └─────────┘    └─────────┘    └─────────┘    └─────────┘         ║
║                                                                       ║
║              PasteFlow + AI: Intelligent Context Management          ║
╚═══════════════════════════════════════════════════════════════════════╝
```

## Executive Summary

This document outlines the comprehensive integration plan for adding an LLM coding agent into PasteFlow. The agent will have full control over the application's context-building features while providing a familiar coding assistant experience similar to Cursor and Claude Code. The integration will maintain the existing UI/CLI functionality while adding a powerful agent panel that seamlessly fits into the current dark-themed, developer-focused interface.

## Visual Design & UI Integration

### Current UI Analysis (Based on Screenshot)

The current PasteFlow interface features:
- **Dark theme** with clean, modern aesthetics (#1a1a1a background, subtle borders)
- **Three-zone layout**: Center content area (instructions + selected files), right sidebar (file tree), left area (currently unused - perfect for agent panel)
- **Header bar** with folder selection, file counts, token tracking, and action buttons
- **Selected files area** at bottom showing file cards with token counts
- **"Packed" state** with Preview and Copy buttons when content is ready

### Proposed Agent Panel Design

```
┌──────────────────────────────────────────────────────────────────────────────┐
│                        Enhanced PasteFlow Layout                             │
├──────────────────────────────┬─────────────────────────────┬────────────────┤
│                              │                             │                │
│  🆕 Agent Terminal (xterm)   │      Content Area           │   File Tree    │
│  [OpenHands CLI Fork]        │   [Instructions + Files]    │   [Sidebar]    │
│                              │                             │                │
│    ╔═══════════════╗        │   ┌─────────────────┐      │   ┌──────┐     │
│    ║ $ openhands   ║        │   │ Instructions...  │      │   ├──📁 src    │
│    ║ > Analyzing.. ║        │   ├─────────────────┤      │   ├──📁 docs   │
│    ║ [Tool: bash]  ║        │   │ Selected Files   │      │   ├──📁 tests  │
│    ║ Executing in  ║        │   └─────────────────┘      │   └──📄 README │
│    ║ terminal below║        │                             │                │
│    ╚═══════════════╝        │   [Pack] [Preview] [Copy]   │                │
│         ⬆️                   │   [Send to Agent 🚀]        │                │
│    OpenHands JSON-RPC       │         ⬇️                   │                │
│    over stdio bridge        │   Packed context transfer   │                │
├──────────────────────────────┴─────────────────────────────┴────────────────┤
│                    💻 Embedded Terminal - User Commands (Collapsible)        │
│ ┌───────────────────────────────────────────────────────────────────────┐   │
│ │ [Agent Commands] [User Terminal] [+]                       [↓] [□] [×] │   │
│ │ $ npm install --save-dev @types/react                                 │   │
│ │ added 1 package in 2s                                                 │   │
│ │ $ grep -r "useMemo" src/ --include="*.tsx"                           │   │
│ │ src/components/file-list.tsx:45: const memoized = useMemo...          │   │
│ │ $ pf select add --path src/app.ts --lines 45-120                     │   │
│ │ ✓ Added to selection                                                  │   │
│ │ $ pf tree --mode complete                                             │   │
│ │ [ASCII tree output visible here]                                      │   │
│ │ $ npm test                                                            │   │
│ │ PASS  All tests passed                                                │   │
│ │ $ git status                                                          │   │
│ │ modified: src/hooks/use-file-selection.ts                             │   │
│ └───────────────────────────────────────────────────────────────────────┘   │
└──────────────────────────────────────────────────────────────────────────────┘
```

#### Layout Architecture
- **Agent Terminal**: Left side, 450px default width (resizable) - xterm.js running OpenHands CLI fork
- **Content Area**: Center, flexible width
- **File Tree**: Right side, maintains current position
- **Embedded Terminal**: Bottom row, spans full width (collapsible) - for agent command visibility & user intervention

#### Dual Terminal Architecture
##### 1. Agent Terminal (Left Panel - OpenHands)
- **Technology**: xterm.js + node-pty running customized OpenHands CLI fork
- **Communication**: JSON-RPC over stdio with Electron main process
- **Display**: Shows agent's reasoning, analysis, and status
- **User Interaction**: Read-only view of agent's internal process

##### 2. Command Execution Terminal (Bottom Panel)
- **Purpose**: All bash commands executed by agent appear here for user visibility
- **Technology**: xterm.js + node-pty with full shell access
- **Features**:
  - Shows ALL commands executed by agent: `pf` CLI, `grep`, `npm`, `git`, etc.
  - User can intervene, cancel, or run manual commands
  - Multiple tabs for agent commands vs user commands
  - Full width spanning all columns
- **Height**: 200px default (resizable from 100px min to 50% viewport max)
- **Collapsible**: Keyboard shortcut (Ctrl/Cmd+`)

#### OpenHands Agent Terminal Structure
```
╔═══════════════════════════════════════════════════════════╗
║  OpenHands Agent Terminal                     [−] [□] [×] ║
╟───────────────────────────────────────────────────────────╢
║  Status: ● Running    Workspace: /Users/<USER>/project       ║
╟───────────────────────────────────────────────────────────╢
║ $ openhands --workspace /Users/<USER>/project                ║
║ OpenHands CLI v0.1.0-pasteflow                            ║
║ Model: gpt-4-turbo | Max iterations: 50                   ║
║ ────────────────────────────────────────────────────────  ║
║ > User: Refactor file selection for performance           ║
║                                                            ║
║ Agent: Analyzing codebase structure...                    ║
║ [Tool: bash] Executing: pf tree --mode complete           ║
║   → Command sent to embedded terminal                     ║
║ [Tool: bash] Executing: grep -n "toggleFileSelection"     ║
║   → Command sent to embedded terminal                     ║
║ [Tool: read_file] Reading: src/hooks/use-file-selection   ║
║   → Via IPC bridge to main process                        ║
║                                                            ║
║ Agent: Found performance bottlenecks:                     ║
║ - Missing React.memo on FileList                          ║
║ - No batch updates in toggleFileSelection                 ║
║ - Re-rendering entire list on single selection            ║
║                                                            ║
║ [Tool: bash] Executing: pf plans create --name "perf-v1"  ║
║   → Command sent to embedded terminal                     ║
║                                                            ║
║ Agent: Plan saved. Ready to implement changes.            ║
║ > Would you like me to apply the optimizations? (y/n)     ║
║ █                                                          ║
╚═══════════════════════════════════════════════════════════╝
```

#### Embedded Terminal (Bottom Panel) - Command Visibility
```
╔═══════════════════════════════════════════════════════════╗
║ [Agent Commands] [User Terminal] [+]          [↓] [□] [×] ║
╟───────────────────────────────────────────────────────────╢
║ ~/project $ pf tree --mode complete                       ║
║ src/                                                       ║
║ ├── components/                                            ║
║ │   ├── file-list.tsx                                     ║
║ │   └── file-card.tsx                                     ║
║ ├── hooks/                                                 ║
║ │   └── use-file-selection.ts                             ║
║ └── utils/                                                 ║
║                                                            ║
║ ~/project $ grep -n "toggleFileSelection" src/hooks/*.ts  ║
║ src/hooks/use-file-selection.ts:45: const toggle...       ║
║ src/hooks/use-file-selection.ts:120: toggle(file)         ║
║                                                            ║
║ ~/project $ npm test -- --watch=false                     ║
║ > project@1.0.0 test                                       ║
║ > jest                                                     ║
║ PASS  src/__tests__/file-selection.test.ts                ║
║                                                            ║
║ ~/project $ git diff src/hooks/use-file-selection.ts      ║
║ @@ -45,7 +45,9 @@                                         ║
║ - setSelectedFiles([...selectedFiles, file]);             ║
║ + unstable_batchedUpdates(() => {                         ║
║ +   setSelectedFiles(prev => [...prev, file]);            ║
║ + });                                                      ║
║                                                            ║
║ ~/project $ pf plans create --name "perf-v1" --content... ║
║ ✓ Plan created: perf-v1 (id: abc123)                      ║
║ ~/project $ █                                              ║
╚═══════════════════════════════════════════════════════════╝
```

#### Visual Styling
```css
/* Main Layout with Terminal */
.app-container {
  display: grid;
  grid-template-rows: auto 1fr auto;
  grid-template-columns: 450px 1fr 300px;
  height: 100vh;
}

.header {
  grid-column: 1 / -1;
}

.main-content {
  display: contents;
}

.terminal-panel {
  grid-column: 1 / -1;
  background-color: var(--background-primary);
  border-top: 1px solid var(--border-color);
  min-height: 100px;
  max-height: 50vh;
  resize: vertical;
  overflow: hidden;
}

/* Agent Panel */
.agent-panel {
  background-color: var(--background-primary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.agent-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-secondary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.agent-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10b981; /* green for connected */
}

.agent-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.agent-message {
  max-width: 85%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: var(--background-secondary);
}

.user-message {
  align-self: flex-end;
  background-color: var(--accent-blue-dim);
  margin-left: auto;
}

/* Terminal Styles */
.terminal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: var(--background-secondary);
  border-bottom: 1px solid var(--border-color);
}

.terminal-tabs {
  display: flex;
  gap: 0.5rem;
}

.terminal-tab {
  padding: 0.25rem 0.75rem;
  background-color: var(--background-primary);
  border-radius: 4px 4px 0 0;
  cursor: pointer;
  font-size: 0.875rem;
}

.terminal-tab.active {
  background-color: var(--background-tertiary);
}

.terminal-content {
  height: calc(100% - 40px);
  background-color: #1e1e1e;
  font-family: 'Cascadia Code', 'Consolas', monospace;
}
```

## Agent Capabilities & App Control

### OpenHands CLI Fork Integration

The agent is a customized fork of OpenHands CLI that:
- Runs in an xterm.js terminal in the left panel
- Communicates via JSON-RPC over stdio with Electron main process
- Executes all commands through the embedded terminal for visibility
- Has filesystem access gated by PasteFlow's PathValidator
- Supports approval workflows for destructive operations

### Intelligent Context Building Workflow

The OpenHands agent follows a sophisticated multi-step process to solve user problems by leveraging PasteFlow's full capabilities:

```
╔════════════════════════════════════════════════════════════════════╗
║              Agent's Intelligent Problem-Solving Pipeline          ║
╠════════════════════════════════════════════════════════════════════╣
║                                                                    ║
║  1. Orientation          2. Discovery           3. Context Build   ║
║  ┌──────────────┐       ┌──────────────┐      ┌──────────────┐   ║
║  │ Parse Intent │  ───▶ │ Tree Overview│  ───▶│ Read Ranges │   ║
║  │ Extract Keys │       │ Grep/Search │       │ Analyze Code │   ║
║  └──────────────┘       └──────────────┘      └──────────────┘   ║
║         │                       │                      │          ║
║         ▼                       ▼                      ▼          ║
║  ┌──────────────┐       ┌──────────────┐      ┌──────────────┐   ║
║  │ pf status    │       │ pf tree      │       │ pf select add│   ║
║  │ Check Context│       │ Map Structure│       │ Line Ranges  │   ║
║  └──────────────┘       └──────────────┘      └──────────────┘   ║
║                                 │                      │          ║
║                                 ▼                      ▼          ║
║                         ┌──────────────┐      ┌──────────────┐   ║
║                         │ pf tokens    │       │ pf content   │   ║
║                         │ Verify Size  │       │ Pack & Send  │   ║
║                         └──────────────┘      └──────────────┘   ║
║                                                                    ║
║  4. Plan Generation      5. Plan Storage        6. Execution      ║
║  ┌──────────────┐       ┌──────────────┐      ┌──────────────┐   ║
║  │ Send to LLM  │  ───▶ │ Save as Plan │  ───▶│ Implement    │   ║
║  │ Get XML Diffs│       │ Markdown+XML │       │ Apply Diffs  │   ║
║  └──────────────┘       └──────────────┘      └──────────────┘   ║
║         │                       │                      │          ║
║         ▼                       ▼                      ▼          ║
║  ┌──────────────┐       ┌──────────────┐      ┌──────────────┐   ║
║  │ 🚀 API Call  │       │ pf plans save│       │ pf select    │   ║
║  │ Full Impl Req│       │ Store in App │       │ clear & add  │   ║
║  └──────────────┘       └──────────────┘      └──────────────┘   ║
║                                                                    ║
╚════════════════════════════════════════════════════════════════════╝
```

#### Example Workflow: Performance Optimization Request

When a user asks "Can you help me refactor the file selection logic to improve performance?", the OpenHands agent:

1. **Orientation Phase - Understanding Current State**
   ```
   [OpenHands Terminal]
   Agent: Starting analysis of file selection performance...
   [Tool: bash] Executing command in embedded terminal
   
   [Embedded Terminal - Bottom Panel]
   $ pasteflow status
   Active workspace: /Users/<USER>/project
   Selection: 0 files
   
   $ pasteflow tree --mode complete
   [Full ASCII tree output visible to user]
   ```

2. **Strategic Discovery Phase - Mapping the Territory**
   ```
   [OpenHands Terminal]
   Agent: Identified key directories from tree analysis
   [Tool: bash] Running targeted searches...
   
   [Embedded Terminal - Bottom Panel]
   $ grep -n "toggleFileSelection\|selectFile" --include="*.ts" -r src/hooks/
   src/hooks/use-file-selection.ts:45: const toggleFileSelection = ...
   
   $ grep -n "use.*[Ff]ile.*[Ss]elect" src/components/*.tsx
   src/components/file-list.tsx:12: import useFileSelection from...
   ```

3. **Precision Reading Phase - Surgical Information Extraction**
   ```
   [OpenHands Terminal]
   [Tool: bash] Getting file metadata
   [Tool: read_file] Reading targeted sections via IPC
   
   [Embedded Terminal - Bottom Panel]
   $ pasteflow files info --path src/hooks/use-file-selection.ts
   Size: 15KB, Lines: 450, Tokens: ~3500
   
   [OpenHands Terminal]
   Agent: Reading lines 45-120 from use-file-selection.ts
   Agent: Found unmemoized component and synchronous updates
   ```

4. **Intelligent Context Assembly**
   ```
   [OpenHands Terminal]
   [Tool: bash] Building optimized context...
   
   [Embedded Terminal - Bottom Panel]
   $ pasteflow select clear
   ✓ Selection cleared
   
   $ pasteflow select add --path src/hooks/use-file-selection.ts --lines 45-120,180-220
   ✓ Added with line ranges
   
   $ pasteflow select add --path src/components/file-list.tsx --lines 80-150
   ✓ Added with line ranges
   
   $ pasteflow tokens selection
   Total: 5,832 tokens (files: 5,832, prompts: 0)
   ```

5. **Dynamic Instruction Generation**
   ```bash
   # Agent creates optimized instruction
   pasteflow instructions create --name "perf-opt-task" --content @- <<EOF
   Analyze the file selection implementation for performance bottlenecks.
   
   Context from tree analysis:
   - Hook pattern: use-file-selection manages state
   - Component: file-list.tsx renders selection
   - Util: file-utils.ts contains helpers
   
   Focus areas:
   - React re-render cycles in FileList component
   - Memoization opportunities in selection hook
   - Batch update patterns for bulk operations
   - Virtual scrolling for lists >100 items
   
   Maintain API compatibility. Provide specific code examples.
   EOF
   ```

6. **Final Assembly and Transmission**
   ```bash
   # Get the complete packed content
   pasteflow content get --max-files 10 --max-bytes 500000
   # Output: Packed content with 5,832 tokens
   
   # 🚀 Send to LLM API for detailed implementation plan
   # Request includes: context, analysis requirements, 
   # and expects response with complete XML diffs
   ```

7. **Plan Generation and Storage**
   ```bash
   # Agent receives implementation plan from LLM with XML diffs
   # Save the plan as a reusable document
   pasteflow plans create --name "perf-optimization-2024-01" --content @- <<EOF
   # Performance Optimization Plan
   
   ## Analysis Summary
   The file selection logic has performance bottlenecks due to...
   
   ## Implementation Changes
   
   ### File: src/hooks/use-file-selection.ts
   <diff>
   <old lines="45-120">
   // Original implementation
   const toggleFileSelection = (file) => {
     setSelectedFiles([...selectedFiles, file]);
   }
   </old>
   <new>
   // Optimized with batch updates
   const toggleFileSelection = useCallback((file) => {
     unstable_batchedUpdates(() => {
       setSelectedFiles(prev => [...prev, file]);
     });
   }, []);
   </new>
   </diff>
   
   ### File: src/components/file-list.tsx
   <diff>
   <old lines="80-150">
   // Component without memoization
   </old>
   <new>
   // With React.memo and useMemo
   </new>
   </diff>
   EOF
   
   # Prompt user for implementation
   echo "Plan saved. Would you like to implement these changes? (y/n)"
   ```

8. **Plan Execution**
   ```bash
   # If user approves, agent implements the plan
   pasteflow select clear
   pasteflow plans select add --id "perf-optimization-2024-01"
   
   # Submit to LLM with implementation prompt
   pasteflow content get | send-to-llm --prompt "Implement this plan"
   ```

### The Tree Command: Agent's Strategic Advantage

The `pf tree` command is the agent's most powerful orientation tool, providing instant architectural understanding that transforms random file exploration into strategic navigation:

```bash
# The Four Tree Modes - Each Serving a Strategic Purpose
pasteflow tree --mode complete        # Full architecture mapping
pasteflow tree --mode selected        # Current context visualization
pasteflow tree --mode selected-with-roots  # Context with hierarchy
pasteflow tree --mode none            # Minimal/no tree output
```

#### Why Tree-First Strategy Wins

```
Traditional Agent Approach           vs.    PasteFlow Agent Approach
─────────────────────────                  ─────────────────────────
1. Receives user query                      1. Receives user query
2. Blindly greps entire codebase           2. Runs 'pf tree --mode complete'
3. Reads random files                       3. Identifies architectural patterns
4. Discovers structure by accident          4. Targets specific directories
5. Wastes tokens on irrelevant files       5. Greps with surgical precision
6. 50,000+ tokens of chaos                 6. 5,000 tokens of relevance

Result: Confused context                    Result: Architectural clarity
```

#### Tree-Informed Discovery Patterns

```typescript
// Agent's tree analysis reveals architectural patterns
const treeInsights = {
  // From: pasteflow tree --mode complete
  componentPattern: "src/components/*.tsx",     // UI components
  hookPattern: "src/hooks/use-*.ts",           // State management
  utilPattern: "src/utils/*-utils.ts",         // Utility functions
  testPattern: "src/__tests__/*-test.ts",      // Test structure
  
  // Discovered conventions
  namingConvention: "kebab-case",              // file-name.tsx
  stateLocation: "hooks/",                     // State in hooks
  apiLocation: "src/api/",                     // API calls
  typesLocation: "src/types/",                 // Type definitions
};

// This knowledge transforms search efficiency
grep "pattern" src/hooks/use-*.ts  // Instead of grep -r "pattern" .
```

### Plans Management System

Plans are implementation blueprints stored as markdown documents with embedded XML diffs. They function as reusable, editable templates for code changes.

#### Plans Architecture
```typescript
interface Plan {
  id: string;
  name: string;
  content: string;  // Markdown with XML diffs
  tokenCount?: number;
  createdAt: string;
  updatedAt: string;
  source: 'agent' | 'manual';
}
```

#### Plans UI Integration
- **Location**: New modal alongside System Prompts, Role Prompts, and Docs
- **Management**: Create, edit, delete, and select plans
- **Selection**: Toggle plans for inclusion in context
- **Token Counting**: Real-time token count for each plan
- **Persistence**: Stored in app database with workspace support

#### CLI Commands for Plans
```bash
# List all plans
pasteflow plans list

# Create a new plan
pasteflow plans create --name "refactor-auth" --content @plan.md

# Update existing plan
pasteflow plans update <id> --content @updated-plan.md

# Delete a plan
pasteflow plans delete <id>

# Select/deselect plans for context
pasteflow plans select add --id <plan-id>
pasteflow plans select remove --id <plan-id>
pasteflow plans select list
pasteflow plans select clear
```

#### Plan Document Format
```markdown
# [Plan Name]

## Objective
Brief description of what this plan accomplishes

## Analysis
Key findings from code analysis

## Implementation

### File: path/to/file.ts
<diff>
<old lines="10-20">
// Original code
const oldImplementation = () => {
  // existing logic
}
</old>
<new>
// Updated code  
const newImplementation = () => {
  // improved logic
}
</new>
</diff>

### File: path/to/another-file.tsx
<diff>
<old lines="45-60">
// Original component
</old>
<new>
// Optimized component
</new>
</diff>

## Testing Strategy
How to verify the changes work correctly

## Rollback Plan
Steps to revert if issues arise
```

### Full Application Control

The agent will have programmatic access to all PasteFlow features through dedicated tool interfaces:

#### 1. File Selection Management
```typescript
interface FileSelectionTools {
  selectFile(path: string, lines?: LineRange[]): Promise<void>
  deselectFile(path: string): Promise<void>
  clearSelection(): Promise<void>
  getSelectedFiles(): Promise<SelectedFileReference[]>
  expandFolder(path: string): Promise<void>
  collapseFolder(path: string): Promise<void>
}
```

#### 2. Prompt Management
```typescript
interface PromptTools {
  // System Prompts
  createSystemPrompt(name: string, content: string): Promise<void>
  editSystemPrompt(id: string, content: string): Promise<void>
  deleteSystemPrompt(id: string): Promise<void>
  toggleSystemPrompt(id: string, enabled: boolean): Promise<void>
  
  // Role Prompts
  createRolePrompt(name: string, content: string): Promise<void>
  editRolePrompt(id: string, content: string): Promise<void>
  toggleRolePrompt(id: string, enabled: boolean): Promise<void>
  
  // Instructions
  setInstructions(content: string): Promise<void>
  appendInstructions(content: string): Promise<void>
  
  // Docs
  addDoc(id: string): Promise<void>
  removeDoc(id: string): Promise<void>
  
  // Plans
  createPlan(name: string, content: string): Promise<{ id: string }>
  editPlan(id: string, content: string): Promise<void>
  deletePlan(id: string): Promise<void>
  listPlans(): Promise<Plan[]>
  selectPlan(id: string): Promise<void>
  deselectPlan(id: string): Promise<void>
  clearSelectedPlans(): Promise<void>
}
```

#### 3. Context Building
```typescript
interface ContextTools {
  packContent(): Promise<PackedContent>
  getTokenCount(): Promise<TokenCounts>
  setFileTreeMode(mode: FileTreeMode): Promise<void>
  applyFilters(patterns: string[]): Promise<void>
  searchFiles(query: string): Promise<FileData[]>
}
```

#### 4. Standard Coding Agent Tools
```typescript
interface CodingTools {
  readFile(path: string): Promise<string>
  writeFile(path: string, content: string): Promise<void>
  editFile(path: string, edits: Edit[]): Promise<void>
  createFile(path: string, content: string): Promise<void>
  deleteFile(path: string): Promise<void>
  renameFile(oldPath: string, newPath: string): Promise<void>
  listFiles(directory: string): Promise<FileInfo[]>
  searchCode(pattern: string, options?: SearchOptions): Promise<SearchResult[]>
}
```

### Terminal Integration

#### Embedded Terminal Architecture

The terminal panel provides a powerful command-line interface integrated directly into PasteFlow, enabling both users and the AI agent to execute commands within the workspace context.

##### Visual Placement & Behavior
- **Position**: Bottom horizontal panel spanning full application width
- **Default Height**: 200px (resizable from 100px min to 50% viewport max)
- **Collapse State**: Fully collapsible with state persistence
- **Keyboard Shortcut**: `Ctrl/Cmd+`` ` for quick toggle
- **Tab Management**: Multiple concurrent terminal sessions
- **Theme**: Matches PasteFlow's dark theme with customizable colors

##### Terminal Stack
```
┌─────────────────────────────────────────────────────────────┐
│                    Terminal Architecture                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│   Renderer Process                 Main Process             │
│   ────────────────                ──────────────           │
│                                                             │
│   ┌──────────────┐               ┌──────────────┐         │
│   │   xterm.js   │◀─────IPC─────▶│   node-pty   │         │
│   │  (Display)   │               │   (Shell)    │         │
│   └──────────────┘               └──────────────┘         │
│          │                               │                  │
│          ▼                               ▼                  │
│   ┌──────────────┐               ┌──────────────┐         │
│   │ Terminal UI  │               │ Path Validator│         │
│   │   Controls   │               │  & Security   │         │
│   └──────────────┘               └──────────────┘         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

##### Implementation Details
```typescript
// Terminal session management
interface TerminalSession {
  id: string
  title: string
  cwd: string
  shell: string
  pid: number
  active: boolean
  output: CircularBuffer<string>
  dimensions: { cols: number; rows: number }
}

// Terminal integration with agent
interface TerminalAgentIntegration {
  // Agent can spawn terminals for specific tasks
  createAgentTerminal(task: string): Promise<TerminalSession>
  
  // Agent monitors command output
  watchTerminalOutput(sessionId: string, pattern: RegExp): Observable<string>
  
  // Agent can inject commands
  executeInTerminal(sessionId: string, command: string): Promise<void>
  
  // Agent can analyze terminal state
  getTerminalContext(sessionId: string): TerminalContext
}

// Security boundaries
interface TerminalSecurity {
  // Workspace confinement
  validateCwd(path: string): boolean
  
  // Command filtering
  filterDangerousCommands(command: string): string | null
  
  // Environment sanitization
  sanitizeEnvironment(env: NodeJS.ProcessEnv): NodeJS.ProcessEnv
  
  // Resource limits
  enforceResourceLimits(session: TerminalSession): void
}
```

#### Terminal Tools for Agent
```typescript
interface TerminalTools {
  // Basic operations
  runCommand(command: string, options?: RunOptions): Promise<CommandResult>
  createTerminal(cwd?: string): Promise<TerminalSession>
  writeToTerminal(sessionId: string, data: string): Promise<void>
  readTerminalOutput(sessionId: string): Promise<string>
  closeTerminal(sessionId: string): Promise<void>
  
  // Advanced operations
  runInteractiveCommand(command: string, interactions: InteractionScript): Promise<void>
  pipeCommandOutput(fromSession: string, toSession: string): Promise<void>
  watchForPattern(sessionId: string, pattern: RegExp): Promise<PatternMatch>
  captureScreenshot(sessionId: string): Promise<TerminalScreenshot>
}
```

#### Terminal UI Component Structure
```tsx
// Terminal panel layout
<TerminalPanel className="terminal-panel">
  <TerminalHeader>
    <TerminalTabs>
      {sessions.map(session => (
        <TerminalTab 
          key={session.id}
          active={session.id === activeSessionId}
          onClick={() => setActiveSession(session.id)}
        >
          <TabIcon type={session.agentOwned ? 'agent' : 'user'} />
          <TabTitle>{session.title}</TabTitle>
          <TabClose onClick={() => closeSession(session.id)} />
        </TerminalTab>
      ))}
      <NewTabButton onClick={createNewSession} />
    </TerminalTabs>
    <TerminalActions>
      <SplitButton onClick={splitTerminal} />
      <ClearButton onClick={clearTerminal} />
      <CollapseButton onClick={toggleCollapse} />
      <MaximizeButton onClick={toggleMaximize} />
    </TerminalActions>
  </TerminalHeader>
  
  <TerminalBody>
    <XTermComponent
      ref={terminalRef}
      options={{
        theme: pasteflowDarkTheme,
        fontSize: 14,
        fontFamily: 'Cascadia Code, Consolas, monospace',
        cursorBlink: true,
        scrollback: 10000,
      }}
      onData={handleTerminalInput}
      onResize={handleTerminalResize}
      onTitleChange={handleTitleChange}
    />
  </TerminalBody>
  
  <ResizeHandle onDrag={handleResize} />
</TerminalPanel>
```

#### Terminal-Agent Interaction Examples

##### Example 1: Agent Running Tests
```typescript
// Agent detects test failure in code analysis
await agent.executeInTerminal('main', 'npm test -- --watch')
const output = await agent.watchTerminalOutput('main', /FAIL|PASS/)

// Agent analyzes failures and suggests fixes
if (output.includes('FAIL')) {
  const failureContext = await agent.getTerminalContext('main')
  const fix = await agent.generateFix(failureContext)
  // Apply fix and re-run tests
}
```

##### Example 2: Build Process Monitoring
```typescript
// Agent starts build in dedicated terminal
const buildSession = await agent.createAgentTerminal('build-process')
await agent.executeInTerminal(buildSession.id, 'npm run build')

// Monitor for errors or warnings
const buildOutput = await agent.watchForPattern(
  buildSession.id, 
  /ERROR|WARNING|✓ Built in/
)

// Provide real-time feedback
agent.streamToChat(`Build ${buildOutput.success ? 'succeeded' : 'failed'}`)
```

##### Example 3: Interactive Debugging
```typescript
// Agent launches debugger
const debugSession = await agent.createAgentTerminal('debug')
await agent.runInteractiveCommand('node --inspect app.js', {
  onPrompt: /debug>/, 
  responses: [
    { match: /Break on start/, response: 'continue' },
    { match: /Paused at/, response: 'backtrace' }
  ]
})
```

#### CLI Integration - The Agent's Swiss Army Knife

The agent leverages PasteFlow's comprehensive CLI toolkit for intelligent context management:

```typescript
interface CLITools {
  executeCliCommand(command: string, args: string[]): Promise<CLIResult>
}
```

##### Navigation & Orientation Commands
```bash
# Understand project structure instantly
pasteflow tree                        # ASCII tree of current selection/mode
pasteflow tree --mode complete        # Full project structure
pasteflow tree --mode selected        # Only selected files
pasteflow tree --list-modes           # Available tree visualization modes

# Check current state
pasteflow status                      # Workspace, paths, selection summary
pasteflow status --include-selection  # Detailed per-file token breakdown
```

##### Intelligent File Discovery
```bash
# File metadata without reading content
pasteflow files info --path src/app.ts
# Output: size, lines, token estimate, binary status

# Selective content retrieval
pasteflow files content --path src/utils.ts
# Returns: content + accurate token count
```

##### Precision Selection Management
```bash
# Surgical file selection with line ranges
pasteflow select add --path src/auth.ts --lines 45-120,200-250
pasteflow select remove --path src/old.ts
pasteflow select clear
pasteflow select list --summary-only    # Quick overview
pasteflow select list --relative        # Relative paths for readability
```

##### Token Budget Management
```bash
# Count tokens before committing to context
pasteflow tokens count --text "analyze this code"
pasteflow tokens selection              # Current selection token total
pasteflow tokens backend                # Which tokenizer is active
```

##### Context Assembly & Export
```bash
# Build and retrieve optimized context
pasteflow content get --max-files 50 --max-bytes 2000000
pasteflow content export --out context.md

# Dynamic instruction management
pasteflow instructions create --name "task-1" --content @prompt.txt
pasteflow instructions update --id "task-1" --content "refined prompt"
pasteflow instructions list
```

##### Workspace Orchestration
```bash
# Switch contexts for different tasks
pasteflow workspaces list
pasteflow workspaces load <id>
pasteflow workspaces create --name "feature-x" --folder /path/to/code
```

##### Async Preview Generation
```bash
# Generate and monitor preview jobs
pasteflow preview start --prompt @analysis.txt --follow
pasteflow preview status <id> --watch
pasteflow preview content <id>
```

### Agent-Plan Workflow

When the agent generates a plan:

1. **Context Analysis**: Agent uses PasteFlow CLI to understand codebase
2. **API Request**: Sends context to LLM with request for implementation plan
3. **Plan Reception**: Receives markdown document with XML diffs
4. **Plan Storage**: Saves plan using `pf plans create`
5. **User Prompt**: "Plan saved. Would you like to implement it?"
6. **Implementation**: If approved:
   - `pf select clear` - Clear current selection
   - `pf plans select add --id <plan-id>` - Select the plan
   - Send to LLM with "Implement this plan" prompt

### Query-Specific Context Building Examples

#### Example 1: Bug Fix Request
**User**: "There's a memory leak in the file viewer component"

**Agent's Strategic Process**:
```bash
# 1. Map the component landscape
pasteflow tree --mode complete | grep -A5 -B5 "file-viewer"
# Discovers: file-viewer.tsx, file-viewer-modal.tsx, use-file-viewer.ts

# 2. Assess file complexity
pasteflow files info --path src/components/file-viewer.tsx
# Output: 850 lines, ~6000 tokens - too large for full inclusion

# 3. Surgical pattern search
grep -n "useEffect\|useState\|useMemo" src/components/file-viewer*.tsx
grep -n "cleanup\|return.*=>\|unmount" src/components/file-viewer*.tsx
grep -n "addEventListener\|observer" src/hooks/use-file-viewer.ts

# 4. Build focused context
pasteflow select clear
pasteflow select add --path src/components/file-viewer.tsx --lines 150-200,420-480
pasteflow select add --path src/hooks/use-file-viewer.ts --lines 45-90

# 5. Verify token budget
pasteflow tokens selection
# Output: 2,100 tokens - optimal for focused analysis

# 6. Generate targeted instruction
pasteflow instructions create --name "memory-leak-fix" --content "
Identify memory leaks in the file viewer component.
Focus on: useEffect cleanup, event listener removal, subscription disposal.
Component structure from tree analysis shows modal and hook separation."
```

#### Example 2: Feature Addition
**User**: "Add keyboard shortcuts for file selection"

**Agent's Architectural Discovery Process**:
```bash
# 1. Understand UI component hierarchy
pasteflow tree --mode complete | grep -E "(list|select|keyboard)"
# Maps: file-list.tsx, file-card.tsx, selection components

# 2. Check current workspace for context
pasteflow status --include-selection
# Confirms workspace is ready, no existing selections

# 3. Discover existing patterns
grep -n "keydown\|keypress\|KeyEvent" -r src/ --include="*.tsx"
grep -n "useKeyboard\|useHotkeys" -r src/hooks/

# 4. Analyze selection architecture
pasteflow files info --path src/hooks/use-file-selection.ts
# 320 lines - need selective reading

# 5. Build implementation context
pasteflow select add --path src/hooks/use-file-selection.ts --lines 1-50,100-150
pasteflow select add --path src/components/file-list.tsx --lines 1-30,200-250
pasteflow select add --path src/types/file-types.ts --lines 1-40

# 6. Check if keyboard utils exist
pasteflow files info --path src/utils/keyboard-utils.ts 2>/dev/null || echo "No existing keyboard utils"

# 7. Generate implementation spec
pasteflow instructions create --name "kbd-shortcuts" --content "
Implement keyboard shortcuts for file selection.
Architecture insights from tree:
- FileList renders items
- use-file-selection manages state
- No existing keyboard utility found

Required shortcuts:
- Ctrl/Cmd+A: Select all visible
- Shift+Arrow: Range selection  
- Ctrl/Cmd+Click: Toggle individual
- Tab: Navigate focus

Integrate with existing selection hooks."
```

#### Example 3: Performance Analysis
**User**: "Why is the app slow when selecting many files?"

**Agent's Performance Investigation Protocol**:
```bash
# 1. Get workspace metrics
pasteflow status --include-selection
# Shows current file count and token totals

# 2. Map performance-critical paths
pasteflow tree --mode complete | grep -E "(worker|cache|virtual|optimize)"
# Identifies: token-worker-pool.ts, virtual-file-loader.ts, cache-service.ts

# 3. Profile token counting operations
pasteflow files info --path src/utils/token-counter.ts
pasteflow files info --path src/utils/token-worker-pool.ts
# Both large files, need targeted analysis

# 4. Find computational hotspots
grep -n "map.*filter\|reduce.*map" src/hooks/use-file-selection.ts
grep -n "forEach.*forEach\|for.*for" src/components/file-list.tsx
grep -n "useMemo\|useCallback" src/components/file-*.tsx | wc -l
# Output: Only 3 memoizations found - potential issue

# 5. Build performance analysis context
pasteflow select clear
pasteflow select add --path src/hooks/use-file-selection.ts --lines 100-200
pasteflow select add --path src/utils/token-worker-pool.ts --lines 1-80
pasteflow select add --path src/components/file-list.tsx --lines 150-250,300-350

# 6. Verify context size
pasteflow tokens selection
# 4,200 tokens - good for detailed analysis

# 7. Create performance analysis prompt
pasteflow instructions create --name "perf-analysis" --content "
Diagnose performance degradation when selecting 100+ files.

Tree analysis reveals:
- token-worker-pool.ts exists (parallel processing available)
- virtual-file-loader.ts suggests lazy loading implemented
- Only 3 useMemo hooks in file components

Symptoms: UI freezes during bulk selection
Focus: O(n²) algorithms, missing memoization, synchronous token counting

Provide specific optimizations with complexity analysis."
```

## Chat Thread Management

### Bidirectional Context Flow

```
╔════════════════════════════════════════════════════════════════════╗
║                   Bidirectional Context Architecture               ║
╠════════════════════════════════════════════════════════════════════╣
║                                                                    ║
║   Agent Chat (Dynamic)          ⟵⟶          Content Area (Manual) ║
║   ─────────────────────                     ─────────────────────  ║
║                                                                    ║
║   ┌──────────────────┐                    ┌──────────────────┐    ║
║   │ @file.ts:45-90  │                    │ Select Files     │    ║
║   │ @utils/helper.js│                    │ Add Instructions │    ║
║   │ @docs/README.md │                    │ Include Prompts  │    ║
║   │ Type message... │                    │ Pack Context     │    ║
║   └──────────────────┘                    └──────────────────┘    ║
║           │                                        │               ║
║           ▼                                        ▼               ║
║   ┌──────────────────┐                    ┌──────────────────┐    ║
║   │ Receives Packed  │◀───────────────────│ [Send to Agent]  │    ║
║   │ Context as Base  │                    │ Button Appears   │    ║
║   └──────────────────┘                    └──────────────────┘    ║
║           │                                                        ║
║           ▼                                                        ║
║   ┌─────────────────────────────┐                                ║
║   │ User can @-mention to add:  │                                ║
║   │ • Additional files          │                                ║
║   │ • Specific line ranges      │                                ║
║   │ • Override context          │                                ║
║   └─────────────────────────────┘                                ║
║                                                                    ║
╚════════════════════════════════════════════════════════════════════╝
```

### Context Building Methods

#### Method 1: Content Area → Agent (Pre-built Context)
Users can meticulously craft their context in the Content Area:
1. Select specific files and line ranges
2. Add custom instructions
3. Include system/role prompts
4. Click **"Send to Agent"** button (appears when content is packed)
5. Context transfers to agent chat with all selections preserved

```
┌─────────────────────────────────────────────────────────────┐
│ Content Area                                               │
├─────────────────────────────────────────────────────────────┤
│ Selected Files: 9 files (~13,220 tokens)                   │
│ Instructions: "Refactor for performance..."                │
│ System Prompts: ✓ Code Review ✓ Best Practices            │
│                                                            │
│ [Pack] ✓ Packed     [Preview]  [Copy]  [Send to Agent 🚀] │
└─────────────────────────────────────────────────────────────┘
```

#### Method 2: Dynamic Context via @-mentions (Cursor-style)
Within the agent chat, users can dynamically include files using @-mentions:

```
╔═══════════════════════════════════════════════════════════╗
║  Agent Chat Input                                         ║
╟───────────────────────────────────────────────────────────╢
║                                                           ║
║  "Can you help me fix the bug in @src/hooks/use-auth.ts ║
║   that's causing issues with @components/Login.tsx:45-89 ║
║   when it calls @api/auth-service.ts?"                   ║
║                                                           ║
║  ┌─────────────────────────────────────────────────┐     ║
║  │ @-mention Autocomplete                          │     ║
║  ├─────────────────────────────────────────────────┤     ║
║  │ 📄 src/hooks/use-auth.ts                       │     ║
║  │ 📄 src/hooks/use-auth-state.ts                 │     ║
║  │ 📄 src/utils/auth-helpers.ts                   │     ║
║  │ 📁 src/api/                                    │     ║
║  └─────────────────────────────────────────────────┘     ║
║                                                           ║
╚═══════════════════════════════════════════════════════════╝
```

### @-mention Functionality

The agent chat supports sophisticated file inclusion through @-mentions:

```typescript
interface AtMentionFeatures {
  // File inclusion patterns
  "@filename.ts"              // Include entire file
  "@path/to/file.ts:45-120"   // Include specific line range
  "@folder/"                  // Include all files in folder
  "@*.test.ts"                // Include files matching pattern
  
  // Special mentions
  "@current"                  // Current packed context
  "@selected"                 // Currently selected files in UI
  "@recent"                   // Recently modified files
  "@related"                  // Files related to current context
  
  // Context modifiers
  "@clear"                    // Clear current context
  "@refresh"                  // Refresh file content
  "@optimize"                 // Let agent optimize context
}
```

### Hybrid Workflow Example

```
┌──────────────────────────────────────────────────────────────┐
│                    Typical User Flow                         │
├──────────────────────────────────────────────────────────────┤
│                                                              │
│  1. User drafts initial context in Content Area             │
│     • Selects core files                                    │
│     • Adds general instructions                             │
│     • Packs context (8,000 tokens)                         │
│                                                              │
│  2. Clicks "Send to Agent" → Context transfers              │
│                                                              │
│  3. In agent chat, refines with @-mentions:                 │
│     "Given the context above, also look at                  │
│      @src/tests/auth.test.ts to understand                  │
│      the expected behavior"                                 │
│                                                              │
│  4. Agent receives:                                         │
│     • Base context (8,000 tokens)                          │
│     • Additional file (500 tokens)                         │
│     • Total: 8,500 tokens                                  │
│                                                              │
│  5. Agent can also modify context programmatically:         │
│     • Add related files it discovers                       │
│     • Remove irrelevant sections                           │
│     • Optimize for token limits                            │
│                                                              │
└──────────────────────────────────────────────────────────────┘
```

### Context State Synchronization

```typescript
interface ContextSyncBehavior {
  // When user sends from Content Area
  onSendToAgent: {
    preserves: ['selectedFiles', 'instructions', 'prompts'],
    creates: 'baseContext',
    enables: 'agentChatWithContext'
  },
  
  // When user @-mentions in chat
  onAtMention: {
    action: 'overlayContext',  // Adds to base without modifying
    updates: 'currentMessageContext',
    preserves: 'baseContext'
  },
  
  // When agent modifies context
  onAgentModification: {
    updates: ['currentThreadContext', 'tokenCount'],
    reflects: 'inUIRealTime',
    allows: 'userOverride'
  }
}
```

### Thread Types
1. **Pre-built Context Thread**: Starts with carefully crafted context from Content Area
2. **Dynamic Context Thread**: Built entirely through @-mentions in chat
3. **Hybrid Thread**: Combines pre-built base with dynamic additions
4. **Agent-Optimized Thread**: Agent refines context based on conversation

## Approval Mechanism

### Permission Levels

#### 1. Safe Operations (No Approval)
- Reading files
- Searching code
- Viewing file trees
- Token counting
- Running read-only commands (ls, git status, etc.)

#### 2. Approval Required (Default)
- File modifications (edit, create, delete)
- Running build/test commands
- Installing dependencies
- Git operations (commit, push)
- System prompt modifications

#### 3. Dangerous Mode
- Toggle: "Skip Permissions" with warning dialog
- Visual indicator: Red border on agent panel
- Auto-disable after session/timeout

### Approval UI
```typescript
interface ApprovalRequest {
  id: string
  type: 'file_edit' | 'command' | 'bulk_operation'
  description: string
  details: {
    // For file edits
    files?: Array<{
      path: string
      diff: string
      additions: number
      deletions: number
    }>
    // For commands
    command?: string
    workingDirectory?: string
  }
  actions: ['approve', 'reject', 'modify']
}
```

Visual representation:
```
╔═══════════════════════════════════════════════════════════════════╗
║                      ⚠️  APPROVAL REQUIRED                        ║
╠═══════════════════════════════════════════════════════════════════╣
║                                                                   ║
║  The agent wants to modify your codebase:                        ║
║                                                                   ║
║  ┌───────────────────────────────────────────────────────────┐   ║
║  │ 📝 File Modifications (3 files)                           │   ║
║  ├───────────────────────────────────────────────────────────┤   ║
║  │                                                           │   ║
║  │ ▼ src/components/agent-panel.tsx                         │   ║
║  │   ┌─────────────────────────────────────────────────┐    │   ║
║  │   │ + Added agent communication handlers             │    │   ║
║  │   │ + Integrated context sync mechanism              │    │   ║
║  │   │ - Removed legacy chat code                       │    │   ║
║  │   └─────────────────────────────────────────────────┘    │   ║
║  │   📊 +45 lines, -12 lines                                │   ║
║  │                                                           │   ║
║  │ ▼ src/hooks/use-agent-state.ts                           │   ║
║  │   ┌─────────────────────────────────────────────────┐    │   ║
║  │   │ + New state management for agent                 │    │   ║
║  │   │ + Tool execution handlers                        │    │   ║
║  │   └─────────────────────────────────────────────────┘    │   ║
║  │   📊 +23 lines, -5 lines                                 │   ║
║  │                                                           │   ║
║  │ ▼ src/main/ipc/agent-routes.ts                           │   ║
║  │   📊 +156 lines (new file)                               │   ║
║  └───────────────────────────────────────────────────────────┘   ║
║                                                                   ║
║  ┌────────────┬────────────┬────────────┬────────────┐          ║
║  │ View Diff  │  Approve   │   Reject   │   Modify   │          ║
║  └────────────┴────────────┴────────────┴────────────┘          ║
║                                                                   ║
╚═══════════════════════════════════════════════════════════════════╝
```

## Architecture Implementation

### OpenHands Fork Requirements

The customized OpenHands CLI fork must:

#### Filesystem Adapter
- Replace direct filesystem access with PasteFlow IPC bridge
- All file operations go through Electron main process PathValidator
- Support read/write/delete with approval workflows

#### Command Execution Bridge
- All bash commands execute in the embedded terminal (bottom panel)
- User can see every command and intervene if needed
- Commands run with workspace-confined cwd

#### Tool Modifications
```python
# OpenHands fork tools/bash.py
class BashTool:
    def execute(self, command: str):
        # Send to PasteFlow embedded terminal via JSON-RPC
        self.rpc_client.send({
            "method": "terminal.execute",
            "params": {
                "sessionId": "agent-commands",
                "command": command
            }
        })
        # Wait for result from terminal
        return self.rpc_client.receive_result()

# OpenHands fork tools/file_ops.py
class FileOpsTool:
    def read_file(self, path: str, lines: Optional[Tuple[int, int]] = None):
        # Route through PasteFlow IPC for path validation
        self.rpc_client.send({
            "method": "file.read",
            "params": {
                "path": path,
                "lines": lines
            }
        })
        return self.rpc_client.receive_result()
```

### Agent System Configuration

The OpenHands agent configuration for PasteFlow integration:

```typescript
const AGENT_SYSTEM_PROMPT = `
You are an intelligent coding assistant integrated with PasteFlow.
You have access to powerful context-building tools that allow you to:

1. Map project architecture instantly with 'pf tree' command
2. Navigate codebases strategically using tree insights before searching
3. Search efficiently using grep with patterns informed by structure
4. Read specific line ranges from files (never read entire large files)
5. Use PasteFlow's comprehensive CLI to build surgical context
6. Generate optimized prompts based on deep understanding
7. Build minimal, focused context (aim for <15,000 tokens when possible)

CRITICAL WORKFLOW RULES:
- ALWAYS start with 'pf tree' to understand project structure
- USE tree output to identify key directories and naming patterns
- THEN use targeted grep based on tree insights
- NEVER blindly read entire files; use 'pf files info' first
- USE line-range selection for files >200 lines
- VERIFY token counts with 'pf tokens selection' before sending
- OPTIMIZE prompts to be specific and actionable

INTELLIGENT CONTEXT BUILDING STRATEGY:
1. Orient: 'pf status' and 'pf tree' to map the territory
2. Discover: Use tree-informed grep patterns for surgical search
3. Assess: 'pf files info' to understand file sizes before reading
4. Extract: Read only relevant line ranges (10-100 lines typically)
5. Assemble: 'pf select add' with precise line ranges
6. Verify: 'pf tokens selection' to confirm size is optimal
7. Generate: Create focused instructions with architectural context
8. Transmit: Pack and send minimal viable context to LLM

CLI MASTERY:
- pf tree: Your map and compass for code navigation
- pf files info: Size reconnaissance before engagement
- pf select add --lines: Surgical precision in context building
- pf tokens selection: Budget verification before transmission
- pf instructions create: Dynamic prompt optimization

Remember: The tree reveals structure. Structure reveals intent. 
Intent guides search. Search enables precision.
A well-architected 5,000 token context beats a blind 50,000 token dump.
`;
```

### Component Structure
```typescript
// Main Agent Panel
<AgentPanel>
  <AgentHeader 
    status={connectionStatus}
    contextInfo={currentContext}
    onNewChat={handleNewChat}
    onSettings={openSettings}
  />
  
  <AgentMessages>
    {messages.map(msg => 
      msg.type === 'user' ? 
        <UserMessage content={msg.content} /> :
        <AgentMessage 
          content={msg.content}
          toolUse={msg.toolUse}
          approvalRequest={msg.approval}
        />
    )}
  </AgentMessages>
  
  <ContextControlBar
    baseContext={baseContext}
    atMentions={currentMentions}
    totalTokens={baseTokens + mentionTokens}
    onClearContext={handleClearContext}
  />
  
  <AgentInput
    onSend={handleSend}
    onAtMention={handleAtMention}
    autocomplete={fileAutocomplete}
    placeholder="Type a message or @mention files..."
  />
</AgentPanel>

// Embedded Terminal (Bottom Panel)
<TerminalPanel position="bottom" resizable collapsible>
  <TerminalHeader>
    <TerminalTabs 
      sessions={terminalSessions}
      activeSession={activeTerminal}
      onNewSession={createTerminal}
    />
    <TerminalControls>
      <CollapseButton onClick={toggleTerminal} />
      <MaximizeButton onClick={maximizeTerminal} />
      <CloseButton onClick={closeTerminal} />
    </TerminalControls>
  </TerminalHeader>
  <XTerminal 
    session={activeTerminal}
    theme={pasteflowDarkTheme}
    onData={handleTerminalInput}
    height={terminalHeight}
  />
</TerminalPanel>
```

### State Management

#### New Hooks
```typescript
// Plans state management
usePlanState() {
  - plans: Plan[]
  - selectedPlans: Plan[]
  - handleAddPlan(plan: Plan)
  - handleDeletePlan(id: string)
  - handleUpdatePlan(updatedPlan: Plan)
  - togglePlanSelection(plan: Plan)
  - getPlans(): { plans: Plan[] }
  - setPlans(plans: { plans: Plan[] })
}

// Agent state management
useAgentChat() {
  - messages: ChatMessage[]
  - sendMessage(content: string, mentions?: FileMention[])
  - clearChat()
  - loadThread(id: string)
  - connectionStatus: ConnectionStatus
  - baseContext: PackedContent | null
  - receivePackedContext(content: PackedContent)
}

// Agent context synchronization
useAgentContext() {
  - baseContext: PackedContent | null
  - currentMentions: FileMention[]
  - syncPackedContent(content: PackedContent)
  - addAtMention(file: string, lines?: LineRange)
  - removeAtMention(file: string)
  - getTotalContext(): CombinedContext
  - getContextSummary(): ContextSummary
}

// @-mention autocomplete
useAtMentionAutocomplete() {
  - searchFiles(query: string): FileResult[]
  - getRecentFiles(): string[]
  - getRelatedFiles(context: string): string[]
  - parseAtMention(text: string): FileMention[]
  - formatAtMention(file: string, lines?: LineRange): string
}

// Terminal management
useTerminal() {
  - sessions: TerminalSession[]
  - createSession(options?: SessionOptions)
  - writeToSession(id: string, data: string)
  - closeSession(id: string)
}

// Approval management
useApprovals() {
  - pendingApprovals: ApprovalRequest[]
  - approve(id: string, modifications?: any)
  - reject(id: string, reason?: string)
  - skipPermissions: boolean
  - toggleSkipPermissions()
}
```

### IPC Architecture

#### OpenHands JSON-RPC Bridge
```typescript
// src/main/ipc/openhands-bridge.ts
class OpenHandsBridge {
  private agentProcess: ChildProcess;
  private terminalSession: TerminalSession;
  
  async start(workspace: string) {
    // Spawn OpenHands CLI fork in Python venv
    this.agentProcess = spawn('python', [
      '-m', 'openhands_pasteflow',
      '--workspace', workspace,
      '--json-rpc'
    ], {
      cwd: workspace,
      env: {
        ...process.env,
        OPENHANDS_MODE: 'pasteflow',
        OPENHANDS_TERMINAL_BRIDGE: 'true'
      }
    });
    
    // Setup JSON-RPC over stdio
    this.setupJsonRpc();
  }
  
  private handleAgentRequest(request: JsonRpcRequest) {
    switch(request.method) {
      case 'terminal.execute':
        // Route to embedded terminal
        return this.terminalSession.execute(request.params.command);
      
      case 'file.read':
        // Validate path and read through main process
        const validation = this.pathValidator.validate(request.params.path);
        if (!validation.valid) throw new Error('Path denied');
        return readFileWithLines(request.params.path, request.params.lines);
      
      case 'plan.create':
        // Save plan to database
        return this.savePlan(request.params);
    }
  }
}
```

#### Terminal Integration Routes
```typescript
// src/main/ipc/terminal-routes.ts
export const terminalRoutes = {
  // Agent terminal (left panel - OpenHands)
  '/agent/terminal/start': startAgentTerminal,
  '/agent/terminal/write': writeToAgentTerminal,
  '/agent/terminal/resize': resizeAgentTerminal,
  
  // Embedded terminal (bottom panel - command execution)
  '/terminal/create': createEmbeddedTerminal,
  '/terminal/execute': executeInEmbeddedTerminal,
  '/terminal/write': writeToEmbeddedTerminal,
  '/terminal/resize': resizeEmbeddedTerminal,
  '/terminal/tabs': manageTerminalTabs,
}

// Event streams for terminal output
terminalEmitter.on('agent-output', (data) => {
  mainWindow.webContents.send('agent-terminal-data', data)
})

terminalEmitter.on('command-output', (data) => {
  mainWindow.webContents.send('embedded-terminal-data', data)
})
```

### Security Implementation

#### Path Validation
```typescript
// Extend existing PathValidator
class AgentPathValidator extends PathValidator {
  validateAgentOperation(
    operation: 'read' | 'write' | 'delete',
    path: string,
    agentId: string
  ): ValidationResult {
    // Check workspace boundaries
    const baseValidation = super.validatePath(path)
    if (!baseValidation.valid) return baseValidation
    
    // Check agent-specific permissions
    if (operation === 'write' && this.isSystemFile(path)) {
      return { valid: false, reason: 'System file modification denied' }
    }
    
    // Audit log
    this.auditLog.record({
      agentId,
      operation,
      path,
      timestamp: Date.now()
    })
    
    return { valid: true }
  }
}
```

## Development Phases

```
┌─────────────────────────────────────────────────────────────────────┐
│                     Implementation Roadmap                          │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  Week 1-2   ▶ Foundation       ████████░░░░░░░░░░░░  40%          │
│  Week 3-4   ▶ App Control      ████████████░░░░░░░░  60%          │
│  Week 5     ▶ Terminal         ██████████████░░░░░░  70%          │
│  Week 6-7   ▶ Agent Backend    ████████████████░░░░  80%          │
│  Week 8-9   ▶ Advanced         ██████████████████░░  90%          │
│  Week 10    ▶ Polish           ████████████████████  100%         │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

### Phase 1: Terminal Foundation (Week 1-2)
```
┌──────────────────────────────────────┐
│ 🏗️  Terminal Infrastructure         │
├──────────────────────────────────────┤
│ □ xterm.js integration (left panel)  │
│ □ Embedded terminal (bottom panel)   │
│ □ node-pty setup in main process     │
│ □ Terminal IPC routes                │
│ □ Tab management for terminals       │
└──────────────────────────────────────┘
```

### Phase 2: OpenHands Integration (Week 3-4)
```
┌──────────────────────────────────────┐
│ 🤖  OpenHands Fork & Bridge         │
├──────────────────────────────────────┤
│ □ Fork OpenHands CLI                 │
│ □ Implement JSON-RPC bridge          │
│ □ File ops adapter (IPC routing)     │
│ □ Terminal execution bridge          │
│ □ Python venv bootstrapper           │
└──────────────────────────────────────┘
```

### Phase 3: Plans System (Week 5)
```
┌──────────────────────────────────────┐
│ 📋  Plans Management                 │
├──────────────────────────────────────┤
│ □ Plans modal UI component           │
│ □ Plans state management hook        │
│ □ CLI commands for plans             │
│ □ Database schema for plans          │
│ □ XML diff parsing/display           │
└──────────────────────────────────────┘
```

### Phase 4: Agent Tools & Security (Week 6-7)
```
┌──────────────────────────────────────┐
│ 🔒  Security & Approval Workflows   │
├──────────────────────────────────────┤
│ □ Path validation for all file ops   │
│ □ Approval UI for destructive ops    │
│ □ Command sandboxing in terminal     │
│ □ Audit logging for agent actions    │
│ □ Rate limiting & resource controls  │
└──────────────────────────────────────┘
```

### Phase 5: Advanced Features (Week 8-9)
```
┌──────────────────────────────────────┐
│ ✨  Enhanced Capabilities            │
├──────────────────────────────────────┤
│ □ Multi-thread chat management       │
│ □ Persistent chat history            │
│ □ Advanced approval workflows        │
│ □ Agent settings & configuration     │
│ □ Performance optimizations          │
└──────────────────────────────────────┘
```

### Phase 6: Polish & Testing (Week 10)
```
┌──────────────────────────────────────┐
│ 🎯  Quality Assurance                │
├──────────────────────────────────────┤
│ □ Comprehensive testing              │
│ □ Performance profiling              │
│ □ Security audit                     │
│ □ Documentation                      │
│ □ Beta release preparation           │
└──────────────────────────────────────┘
```

## Configuration & Settings

### Agent Settings Panel
```typescript
interface AgentSettings {
  // Model settings
  model: 'gpt-4' | 'claude-3' | 'local'
  apiKey?: string
  endpoint?: string
  
  // Behavior settings
  autoApprove: {
    readOperations: boolean
    safeCommands: boolean
    testCommands: boolean
  }
  
  // Context settings
  maxContextTokens: number
  includeSystemPrompts: boolean
  includeGitHistory: boolean
  
  // Terminal settings
  defaultShell: string
  terminalFontSize: number
  
  // Safety settings
  requireApproval: boolean
  auditLogging: boolean
  maxFileSize: number
  blockedPaths: string[]
}
```

## Performance Considerations

### Optimization Strategies
1. **Lazy Loading**: Load agent components only when panel is opened
2. **Message Virtualization**: Use react-window for long chat histories
3. **Streaming Responses**: Implement backpressure handling
4. **Context Caching**: Cache packed content with signatures
5. **Debounced Updates**: Batch UI updates during rapid changes

### Memory Management
```typescript
class AgentMemoryManager {
  private maxMessages = 1000
  private maxTerminalBuffer = 10000
  
  trimChatHistory() {
    // Keep recent messages, summarize older ones
  }
  
  clearInactiveTerminals() {
    // Close terminals inactive > 30 minutes
  }
  
  compactContext() {
    // Remove duplicate file selections
    // Merge similar prompts
  }
}
```

## Error Handling & Recovery

### Error States
1. **Connection Errors**: Show reconnection UI with retry
2. **Tool Failures**: Graceful degradation with user notification
3. **Approval Timeout**: Auto-reject after configurable timeout
4. **Context Overflow**: Suggest context reduction strategies

### Recovery Mechanisms
```typescript
class AgentErrorRecovery {
  async handleError(error: AgentError) {
    switch (error.type) {
      case 'CONNECTION_LOST':
        await this.attemptReconnection()
        break
      case 'TOOL_FAILURE':
        await this.fallbackToManualMode(error.tool)
        break
      case 'CONTEXT_TOO_LARGE':
        await this.suggestContextReduction()
        break
    }
  }
}
```

## Testing Strategy

### Unit Tests
- Component rendering tests
- Hook behavior tests
- IPC message validation
- Tool execution tests

### Integration Tests
- Agent-to-app communication
- File operation workflows
- Terminal interaction
- Approval workflows

### E2E Tests
- Complete chat sessions
- Context building workflows
- Multi-tool operations
- Error recovery scenarios

## Security Considerations

```
╔══════════════════════════════════════════════════════════════════╗
║                    🔒 Security Architecture                      ║
╠══════════════════════════════════════════════════════════════════╣
║                                                                  ║
║  ┌──────────────┐     ┌──────────────┐     ┌──────────────┐   ║
║  │   Renderer   │────▶│  Main Process │────▶│    Agent     │   ║
║  │   (Isolated) │     │  (Gatekeeper) │     │  (Sandboxed) │   ║
║  └──────────────┘     └──────────────┘     └──────────────┘   ║
║         │                     │                     │           ║
║         │                     ▼                     │           ║
║         │            ┌──────────────┐               │           ║
║         └───────────▶│ Path Validator│◀─────────────┘           ║
║                      └──────────────┘                           ║
║                              │                                  ║
║                              ▼                                  ║
║                      ┌──────────────┐                           ║
║                      │  Audit Log   │                           ║
║                      └──────────────┘                           ║
╚══════════════════════════════════════════════════════════════════╝
```

### Threat Model
```
┌─────────────────────────────────────────────────────────────┐
│ Threat              │ Risk Level │ Mitigation                │
├─────────────────────┼────────────┼───────────────────────────┤
│ Malicious Commands  │    HIGH    │ Command validation        │
│ Path Traversal      │    HIGH    │ Workspace boundaries      │
│ Resource Exhaustion │   MEDIUM   │ Rate limiting & quotas    │
│ Credential Leakage  │    HIGH    │ Encrypted key storage     │
│ Code Injection      │    HIGH    │ Input sanitization        │
└─────────────────────┴────────────┴───────────────────────────┘
```

### Mitigation Strategies
- **Process Isolation**: Sandbox agent processes with minimal privileges
- **Audit Trail**: Log all operations with timestamps and agent IDs
- **Data Encryption**: Secure storage for API keys and sensitive data
- **Rate Limiting**: Prevent API abuse and resource exhaustion
- **Path Validation**: Strict workspace boundaries enforcement
- **Output Sanitization**: Clean terminal output and user inputs

## Future Enhancements

```
┌────────────────────────────────────────────────────────────────┐
│                    🚀 Future Roadmap                           │
├────────────────────────────────────────────────────────────────┤
│                                                                │
│  v2.0  ├─ Multi-Agent Collaboration                           │
│        ├─ Custom Tool Development                             │
│        └─ Voice Interaction                                   │
│                                                                │
│  v2.5  ├─ Advanced Diff Preview                               │
│        ├─ Session Replay System                               │
│        └─ Workflow Templates                                  │
│                                                                │
│  v3.0  ├─ Metrics Dashboard                                   │
│        ├─ Plugin Ecosystem                                    │
│        └─ Cloud Sync                                          │
│                                                                │
└────────────────────────────────────────────────────────────────┘
```

### Planned Features
1. **Multi-Agent Collaboration**: Multiple specialized agents working together
2. **Custom Tools**: User-defined agent tools and actions
3. **Voice Interaction**: Speech-to-text input with wake word
4. **Diff Preview**: Side-by-side diff viewing with inline editing
5. **Replay System**: Record and replay agent sessions for learning
6. **Templates**: Saved agent workflows and automation scripts
7. **Metrics Dashboard**: Token usage, success rates, performance stats
8. **Plugin System**: Third-party agent extensions and integrations

## Efficiency Gains vs Traditional Agents

```
┌─────────────────────────────────────────────────────────────────────┐
│                   Traditional Agent vs PasteFlow Agent              │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  Traditional Agent 😔                 PasteFlow Agent 🚀           │
│  ─────────────────                   ──────────────────           │
│                                                                     │
│  • Blind file exploration            • Tree-guided navigation      │
│  • Dumps entire files                • Surgical line selection     │
│  • 50,000-200,000 tokens            • 5,000-15,000 tokens        │
│  • Generic prompts                   • Architecture-aware prompts  │
│  • Random grep patterns              • Tree-informed search        │
│  • Manual file discovery             • CLI-powered automation      │
│  • No structural awareness           • Full architectural context  │
│  • Static conversation               • Dynamic context refinement  │
│                                                                     │
│  Workflow:                           Workflow:                     │
│  1. grep -r "pattern" .              1. pf tree --mode complete   │
│  2. cat entire_file.ts               2. pf files info --path ...  │
│  3. "Too many tokens!"               3. pf select add --lines ... │
│  4. Try again with less              4. pf tokens selection       │
│  5. Still missing context            5. Perfect context, first try│
│                                                                     │
│  Result: Slow, expensive,            Result: Fast, precise,       │
│          incomplete context                  architectural clarity │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

### Comparative Analysis

#### Discovery Efficiency
- **Traditional**: Random exploration, discovers structure by accident
- **PasteFlow Agent**: Tree-first mapping, understands architecture instantly
- **Advantage**: 10x faster file discovery with architectural awareness

#### Token Efficiency
- **Traditional**: Entire files or repositories (50,000-200,000 tokens)
- **PasteFlow Agent**: Precise line ranges (5,000-15,000 tokens)
- **Savings**: 70-90% reduction = faster responses, lower costs, higher quality

#### Context Quality
- **Traditional**: "Here's my entire codebase, find the bug"
- **PasteFlow Agent**: "Based on tree analysis, here are the exact hook implementations at lines 45-120, with their component consumers at lines 200-250, filtered for performance patterns"

#### Tool Orchestration
- **Traditional**: Basic read/write operations
- **PasteFlow Agent**: 20+ specialized CLI commands working in concert
- **Power Multiplier**: Each CLI command eliminates dozens of manual steps

#### Time to Solution
- **Traditional**: 5-10 back-and-forth messages to gather context
- **PasteFlow Agent**: Single, architecturally-informed query with optimal context

## Key Architectural Decisions

### OpenHands CLI Fork as Agent
- **Not a chat interface**: The agent is a forked OpenHands CLI running in an xterm.js terminal
- **JSON-RPC over stdio**: Communication between OpenHands Python process and Electron main
- **All tools bridged**: File ops and bash commands routed through PasteFlow's security layer

### Dual Terminal System
1. **Agent Terminal (Left Panel)**: Shows OpenHands internal processing and reasoning
2. **Embedded Terminal (Bottom Panel)**: Executes all CLI commands with user visibility

### Command Visibility & Control
- Every bash command executed by the agent appears in the embedded terminal
- Includes: `pf` CLI commands, `grep`, `npm`, `git`, `python`, build tools, etc.
- Users can see exactly what's happening and intervene if needed
- Commands run in workspace-confined environment with path validation

### Plans as First-Class Citizens
- Plans are markdown documents with embedded XML diffs
- Stored in database alongside System Prompts, Role Prompts, and Docs
- Can be created by agent or manually by users
- Selected into context for implementation

## Conclusion

```
╔═════════════════════════════════════════════════════════════════════╗
║                                                                     ║
║                          PasteFlow + AI                            ║
║                                                                     ║
║   Context Building  +  Intelligent Agent  =  Developer Superpowers ║
║                                                                     ║
║   ┌─────────────┐        ┌─────────────┐        ┌─────────────┐  ║
║   │   SEARCH    │   ───▶  │   BUILD     │   ───▶ │   SOLVE     │  ║
║   │  Codebase   │        │   Context   │        │   Problem   │  ║
║   └─────────────┘        └─────────────┘        └─────────────┘  ║
║                                                                     ║
║            Intelligent • Efficient • Context-Aware                 ║
║                                                                     ║
╚═════════════════════════════════════════════════════════════════════╝
```

This comprehensive integration plan establishes PasteFlow as the next evolution in AI-assisted development, where the agent becomes a true architectural navigator rather than a blind file reader.

## Revolutionary Advantages

### The Tree-First Paradigm
The `pf tree` command fundamentally changes how AI agents understand codebases. Instead of stumbling through files hoping to find relevance, the PasteFlow agent **sees the forest before examining the trees**. This architectural awareness transforms every subsequent operation from guesswork into strategic precision.

### CLI Command Symphony
With 20+ specialized CLI commands at its disposal, the agent orchestrates a symphony of tools:
- **Navigation**: `pf tree` provides the map
- **Reconnaissance**: `pf files info` scouts without reading
- **Precision**: `pf select add --lines` surgically extracts relevance
- **Verification**: `pf tokens selection` ensures optimal sizing
- **Optimization**: `pf instructions create` crafts perfect prompts

Each command eliminates what would be dozens of manual operations or thousands of wasted tokens in traditional approaches.

### Bidirectional Context Flow
Users can meticulously craft context in the Content Area and send it to the agent, or dynamically build context through @-mentions in chat. This flexibility means users can work in whatever mode suits their task—deliberate curation or rapid iteration.

### Architectural Intelligence
The agent doesn't just read code; it understands architecture:
- Naming conventions discovered through tree analysis
- Directory structures that reveal design patterns  
- File relationships mapped before investigation
- Component hierarchies understood at a glance

This architectural intelligence means the agent can answer not just "what" but "why"—understanding design decisions, architectural patterns, and system relationships that would be invisible to traditional file-dumping approaches.

## The Bottom Line

**Traditional agents** are like tourists with a phrase book, randomly asking for directions and hoping to stumble upon their destination.

**PasteFlow's agent** is like a local guide with a detailed map, knowing exactly which streets to take, which shortcuts exist, and how to reach the destination efficiently.

The result is not just faster or cheaper—it's fundamentally more intelligent. The agent provides answers grounded in architectural understanding, delivers context with surgical precision, and accomplishes in one interaction what traditionally required extended conversations.

This integration plan doesn't just add an AI agent to PasteFlow; it redefines what an AI coding assistant can be when given the right tools and architectural awareness. The phased implementation ensures we can deliver value quickly while building toward a future where AI truly understands not just code, but codebases as living, architected systems.