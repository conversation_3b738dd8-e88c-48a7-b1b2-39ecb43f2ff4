import { useEffect } from 'react';

import { memoryMonitor } from '../utils/memory-monitor';
import { enhancedFileContentCache } from '../utils/enhanced-file-cache-adapter';
import { tokenCountCache } from '../utils/token-cache-adapter';
import { getTreeSortingService } from '../utils/tree-sorting-service';
import { getFlattenCacheStats } from '../utils/tree-node-transform';

/**
 * Hook to set up memory monitoring for all application caches.
 * Registers caches with the memory monitor and starts periodic monitoring.
 */
export function useMemoryMonitoring(): void {
  // Run effect only once on mount to avoid re-registration loops
  useEffect(() => {
    // Register file content cache
    memoryMonitor.registerCache(
      'FileContentCache',
      () => enhancedFileContentCache.getMetrics().totalEntries,
      () => enhancedFileContentCache.getMemoryUsageMB()
    );

    // Register token count cache
    memoryMonitor.registerCache(
      'TokenCountCache',
      () => tokenCountCache.size(),
      () => tokenCountCache.estimateMemoryUsage()
    );

    // Note: DirectorySelectionCache registration removed to prevent re-render loops
    // The cache is still functional but not monitored for memory usage

    // Register tree sorting cache
    const treeSortingService = getTreeSortingService();
    memoryMonitor.registerCache(
      'TreeSortingCache',
      () => treeSortingService.getCacheStats().entries,
      () => {
        const stats = treeSortingService.getCacheStats();
        return stats.estimatedMemory / (1024 * 1024);
      }
    );

    // Register flatten cache
    memoryMonitor.registerCache(
      'FlattenCache',
      () => getFlattenCacheStats().size,
      () => getFlattenCacheStats().estimatedMemoryMB
    );

    // Set thresholds based on typical Electron app constraints
    memoryMonitor.setThresholds(50, 100); // Warn at 50MB, critical at 100MB

    // Start periodic monitoring (every 30 seconds in production, every 5 seconds in dev)
    const intervalMs = process.env.NODE_ENV === 'development' ? 5000 : 30_000;
    const cleanup = memoryMonitor.startPeriodicMonitoring(intervalMs);

    // Log initial stats in development
    if (process.env.NODE_ENV === 'development') {
      const stats = memoryMonitor.getStats();
      console.log('Memory monitoring initialized:', {
        totalCaches: stats.caches.length,
        totalMemoryMB: stats.totalMemoryMB.toFixed(2)
      });
    }

    return () => {
      cleanup();
      memoryMonitor.unregisterCache('FileContentCache');
      memoryMonitor.unregisterCache('TokenCountCache');
      memoryMonitor.unregisterCache('TreeSortingCache');
      memoryMonitor.unregisterCache('FlattenCache');
    };
  }, []); // Run only once on mount - caches are registered globally
}