:root {
  --background-primary: #e6e6e6;
  --background-secondary: #dedede;
  --background-selected: rgba(0,0,0,0.08);
  --background-elevated: #f8f8f8;
  --accent-blue: #0E639C;
  --border-color: #c0c0c0;
  --hover-color: #d3d3d3;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-disabled: #aaaaaa;
  --icon-color: #555555;
  --success-color: #1e8449;
  --warning-color: #f39c12;
  --error-color: #e74c3c;
  --primary-button-background: #6d6d6d;
  --primary-button-text: #ffffff;
  --file-card-selected-border: #808080;
  --checkbox-border: #808080;
  --checkbox-background: transparent;
  --icon-stroke-color: #808080;
  --icon-hover-stroke-color: #666666;
  --scrollbar-track: #cecece;
  --scrollbar-thumb: #aaaaaa;
  --scrollbar-thumb-hover: #888888;
  --option-selected-bg: #333333;
  --option-selected-text: #ffffff;
  
  /* Workspace drag-and-drop */
  --workspace-item-height: 44px;
  --workspace-drag-opacity: 0.5;
  --workspace-drag-transition: 200ms;
  
  /* Dropdown Typography Scale */
  --dropdown-font-size-xs: 11px;
  --dropdown-font-size-sm: 12px;
  --dropdown-font-size-base: 12px;
  --dropdown-font-size-md: 14px;
  
  /* Dropdown Font Weights */
  --dropdown-font-light: 300;
  --dropdown-font-regular: 400;
  --dropdown-font-medium: 500;
  
  /* Dropdown Line Heights */
  --dropdown-line-height-tight: 1.3;
  --dropdown-line-height-base: 1.5;
  --dropdown-line-height-relaxed: 1.6;
  
  /* Dropdown Letter Spacing */
  --dropdown-letter-spacing-tight: -0.01em;
  --dropdown-letter-spacing-base: 0.02em;
  --dropdown-letter-spacing-wide: 0.04em;
  
  /* Glass & Transparency Effects */
  --dropdown-glass-bg: rgba(245, 245, 247, 0.85);
  --dropdown-glass-bg-dark: rgba(30, 30, 30, 0.85);
  --dropdown-glass-border: rgba(255, 255, 255, 0.18);
  --dropdown-glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  
  /* Backdrop Effects */
  --dropdown-backdrop-blur: 20px;
  --dropdown-backdrop-saturate: 180%;
  
  /* Gradients */
  --dropdown-gradient-subtle: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 100%);
  --dropdown-gradient-hover: linear-gradient(90deg,
    rgba(14, 99, 156, 0.08) 0%,
    rgba(14, 99, 156, 0.12) 100%);
  --dropdown-gradient-active: linear-gradient(135deg,
    rgba(14, 99, 156, 0.15) 0%,
    rgba(14, 99, 156, 0.25) 100%);
  
  /* Accent Colors */
  --dropdown-accent-primary: #0E639C;
  --dropdown-accent-glow: rgba(14, 99, 156, 0.4);
  --dropdown-accent-subtle: rgba(14, 99, 156, 0.06);
  
  /* State Colors */
  --dropdown-hover-bg: rgba(14, 99, 156, 0.08);
  --dropdown-active-bg: rgba(14, 99, 156, 0.15);
  --dropdown-focus-glow: 0 0 0 3px rgba(14, 99, 156, 0.25);
  
  /* Animation Timing */
  --dropdown-easing-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --dropdown-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --dropdown-duration-fast: 0.15s;
  --dropdown-duration-base: 0.2s;
  --dropdown-duration-slow: 0.3s;
}

.dark-mode {
  --background-primary: #1E1E1E;
  --background-secondary: #252526;
  --background-selected: #2A2D2E;
  --background-elevated: #2D2D30;
  --accent-blue: #0E639C;
  --border-color: #3E3E42;
  --hover-color: #333333;
  --text-primary: #E8E8E8;
  --text-secondary: #BBBBBB;
  --text-disabled: #808080;
  --icon-color: #CCCCCC;
  --success-color: #2ecc71;
  --warning-color: #f39c12;
  --error-color: #e74c3c;
  --primary-button-background: #0E639C;
  --primary-button-text: #ffffff;
  --file-card-selected-border: #3E3E42;
  --checkbox-border: #666666;
  --checkbox-background: transparent;
  --icon-stroke-color: #666666;
  --icon-hover-stroke-color: #808080;
  --scrollbar-track: #1E1E1E;
  --scrollbar-thumb: #3E3E42;
  --scrollbar-thumb-hover: #505050;
  --option-selected-bg: #dedede;
  --option-selected-text: #333333;
  
  /* Workspace drag-and-drop */
  --workspace-item-height: 44px;
  --workspace-drag-opacity: 0.5;
  --workspace-drag-transition: 200ms;
  
  /* Dark Mode Glass Effects */
  --dropdown-glass-bg: rgba(40, 40, 40, 0.85);
  --dropdown-glass-border: rgba(255, 255, 255, 0.08);
  --dropdown-glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  
  /* Dark Mode Gradients */
  --dropdown-gradient-subtle: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.05) 0%, 
    rgba(255, 255, 255, 0.02) 100%);
  --dropdown-gradient-hover: linear-gradient(90deg,
    rgba(14, 99, 156, 0.12) 0%,
    rgba(14, 99, 156, 0.18) 100%);
  --dropdown-gradient-active: linear-gradient(135deg,
    rgba(14, 99, 156, 0.2) 0%,
    rgba(14, 99, 156, 0.3) 100%);
  
  /* Dark Mode States */
  --dropdown-hover-bg: rgba(255, 255, 255, 0.08);
  --dropdown-active-bg: rgba(14, 99, 156, 0.25);
  --dropdown-accent-subtle: rgba(14, 99, 156, 0.08);
} 