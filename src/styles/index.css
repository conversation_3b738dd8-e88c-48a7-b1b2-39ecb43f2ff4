/* Import Radix UI Dialog styles */
@import './radix-modal.css';
/* Import color variables */
@import './colors.css';
/* Import local fonts */
@import './fonts.css';

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
    Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--background-primary);
  color: var(--text-primary);
  line-height: 1.5;
  height: 100vh;
  overflow: hidden;
}

#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

button {
  cursor: pointer;
  font-family: inherit;
  border: 0.0625rem solid var(--border-color);
  background-color: var(--background-primary);
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  height: 2rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  white-space: nowrap;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-primary);
}

/* Ensure icon-only buttons maintain size */
.workspace-button {
  width: 2rem; /* Match height */
  padding: 0; /* Remove padding if only icon */
  gap: 0; /* Remove gap if only icon */
}

button:hover {
  background-color: var(--hover-color);
}

button:focus {
  outline: none;
  outline-offset: 0.0625rem;
}

button.primary {
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  border-color: var(--primary-button-background);
}

button.primary:hover {
  background-color: var(--primary-button-background);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

input[type="text"],
input[type="search"] {
  padding: 0.5rem 0.75rem;
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  font-family: inherit;
  font-size: 0.875rem;
  outline: none;
  width: 100%;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

input[type="text"]:focus,
input[type="search"]:focus {
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 0.0625rem var(--accent-blue);
}

.monospace {
  font-family: Consolas, Menlo, Monaco, "Courier New", monospace;
  line-height: 1.5;
}

/* Main layout structure */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* MIGRATED TO: src/components/app-header.css */
/* .header, .header h1, .app-title, .folder-name-container styles */

/* REMOVED: Unused style - .folder-name was not referenced anywhere */

/* MIGRATED TO: src/components/app-header.css */
/* .folder-info and .selected-folder styles */

.select-folder-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-secondary);
  border: none;
  border-radius: 0.375rem;
  color: var(--text-primary);
  padding: 0.25rem;
  height: 2rem;
  width: 2rem;
  transition: background-color 0.2s ease;
  border: 0.0625rem solid var(--icon-stroke-color);
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}

.select-folder-btn.large {
  width: 4rem;
  height: 4rem;
}

.select-folder-btn svg {
  stroke: var(--icon-stroke-color);
  stroke-width: 0.09375rem;
}

.select-folder-btn:focus {
  outline-width: 0.03125rem;
  background-color: var(--hover-color);
}

.select-folder-btn:focus svg,
.select-folder-btn:hover svg {
  stroke: var(--icon-hover-stroke-color);
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: 100%;
  /* With left-docked agent, use normal row so order is Left→Center→Right */
  flex-direction: row;
}

/* Left-docked Agent Panel */
.agent-panel.left-dock {
  width: 20rem; /* 320px default */
  min-width: 16rem;
  max-width: 28rem;
  flex: 0 0 auto;
  background-color: var(--background-secondary);
  border-right: 0.0625rem solid var(--border-color);
  padding: 0.75rem; /* appropriate padding around contents */
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.agent-panel .agent-header strong {
  font-size: 0.875rem;
}

.agent-panel .agent-messages {
  background: var(--background-primary);
}

/* Agent @-Mention Autocomplete Styling */
.file-autocomplete-dropdown {
  position: absolute;
  /* top is set dynamically by the input; do not set bottom here */
  left: 0;
  width: 25rem; /* 400px */
  max-height: 18.75rem; /* 300px */
  background: var(--background-secondary);
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.5rem;
  box-shadow: 0 -0.25rem 0.75rem rgba(0, 0, 0, 0.15);
  overflow-y: auto;
  z-index: 1000;
}

.autocomplete-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: background-color 0.1s;
}

.autocomplete-item.selected {
  background: var(--accent-blue-dim);
}

.autocomplete-item:hover {
  background: var(--hover-background);
}

.file-info { flex: 1; margin-left: 0.5rem; }
.file-path { font-size: 0.875rem; color: var(--text-primary); }
.file-meta { font-size: 0.75rem; color: var(--text-secondary); margin-top: 0.125rem; }
.highlight-match { font-weight: 600; color: var(--accent-blue); }

.file-preview-pane {
  position: absolute;
  left: 100%;
  top: 0;
  width: 25rem;
  max-height: 18.75rem;
  margin-left: 0.5rem;
  padding: 0.75rem;
  background: var(--background-primary);
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.5rem;
  overflow: auto;
}

.file-preview-pane pre {
  font-size: 0.75rem;
  line-height: 1.4;
  color: var(--text-secondary);
}

.sidebar {
  width: 18.75rem;
  min-width: 12.5rem;
  max-width: 31.25rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-left: 0.0625rem solid var(--border-color);
  overflow: hidden;
  background-color: var(--background-secondary);
  position: relative;
  transition: width 0.1s ease;
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 0.0625rem solid var(--border-color);
  background-color: var(--background-secondary);
}

.sidebar-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.sidebar-folder-path {
  font-size: 0.75rem;
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sidebar-search {
  background-color: var(--background-secondary);
}

.sidebar-buttons {
  display: flex;
  align-items: center;
  background-color: var(--background-secondary);
  border-bottom: 0.0625rem solid var(--border-color);
  padding: 0.5rem 1rem;
  gap: 0.5rem;
  height: 3rem;
}

.sidebar-button {
  min-width: 1.75rem;
  height: 1.75rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-secondary);
  border: 0.0625rem solid var(--icon-stroke-color);
  border-radius: 0.375rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}

.sort-dropdown-container {
  position: relative;
  width: 25%;
}

.sidebar-buttons > .sidebar-button {
  width: 25%;
}

.sidebar-button:active {
  transform: translateY(0);
}

.sidebar-button:focus {
  outline-width: 0.03125rem;
  background-color: var(--hover-color);
}

.sidebar-button svg {
  stroke: var(--icon-stroke-color);
  fill: var(--background-primary);
  stroke-width: 0.09375rem;
}

.sidebar-button svg path {
  fill: transparent;
}

.sidebar-button:focus svg {
  stroke: var(--icon-hover-stroke-color);
}

.sort-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: var(--background-color);
  border-radius: 0.375rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.sort-dropdown-file-tree {
  min-width: 220px;
  background-color: var(--background-primary);
}

.sort-dropdown-button {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  font-weight: 400;
  text-align: left;
  gap: 0.375rem;
  border-radius: 0.375rem;
  background-color: var(--background-secondary);
  color: var(--text-color);
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.sort-dropdown-button svg {
  stroke: var(--icon-stroke-color);
  stroke-width: 0.09375rem;
}

.sort-dropdown button span {
  margin-right: 0.5rem;
}

.sort-dropdown button .checkmark {
  margin-left: auto;
  margin-right: 0;
  color: var(--dropdown-accent-primary);
}

.sort-indicator {
  font-family: 'SF Mono', monospace;
  font-size: 11px;
  color: var(--text-secondary);
  margin-left: auto;
  opacity: 0.7;
}

.sidebar-buttons .sidebar-button {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.625rem;
}

.sidebar-actions {
  display: flex;
  padding: 0.75rem 1rem;
  gap: 0.5rem;
  border-bottom: 0.0625rem solid var(--border-color);
  background-color: var(--background-secondary);
}

.sidebar-action-btn {
  flex: 1;
  font-size: 0.8125rem;
  padding: 0.375rem 0.75rem;
  background-color: var(--background-primary);
  color: var(--text-primary);
  border: 0.0625rem solid var(--border-color);
}

.file-tree {
  flex: 1;
  padding: 0.5rem 0;
  background-color: var(--background-secondary);
}

.folder-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
  flex-shrink: 0;
  color: var(--icon-color);
}

.tree-item-icon svg,
.folder-icon svg {
  stroke: var(--checkbox-border);
}

.folder-path {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.tree-empty {
  padding: 1rem;
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.tree-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem 1rem;
  gap: 0.75rem;
  color: var(--text-secondary);
}

.tree-loading .spinner {
  width: 1.5rem;
  height: 1.5rem;
}

.folder-header {
  background-color: var(--background-secondary);
  font-weight: 500;
  position: sticky;
  top: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.folder-header-left {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.folder-select-checkbox {
  margin-right: 0.5rem;
  cursor: pointer;
}

.folder-actions {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.folder-action-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.25rem;
  cursor: pointer;
  padding: 0;
  border-radius: 0.25rem;
  transition: background-color 0.2s, color 0.2s;
}

.folder-action-btn:hover {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
}

.folder-action-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.125rem var(--primary-color-transparent);
}

.folder-action-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  color: var(--text-muted);
}

.folder-action-btn:disabled:hover {
  background: none;
  color: var(--text-muted);
}

.tree-item {
  display: flex;
  align-items: center;
  padding: 0.125rem 0.375rem;
  margin: 0;
  border-radius: 0.25rem;
  align-items: center;
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: background-color 0.1s ease;
  color: var(--text-primary);
}

.tree-item:hover {
  background-color: var(--hover-color);
}

.tree-item.selected {
  background-color: var(--background-selected);
}

.tree-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
  flex-shrink: 0;
  color: var(--icon-color);
}

.tree-item-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.tree-item-toggle {
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.25rem;
  cursor: pointer;
  color: var(--icon-color);
  z-index: 2;
}

.tree-item-toggle svg {
  transition: transform 0.15s ease-in-out;
  transform: rotate(0deg);
}

.tree-item-toggle.expanded svg {
  transform: rotate(90deg);
}

.tree-item-indent {
  width: 1rem;
  flex-shrink: 0;
}

.tree-item-checkbox {
  cursor: pointer;
  position: relative;
  z-index: 1;
  opacity: 0;
}

.tree-item-checkbox-container {
  margin-right: 0.5rem;
  cursor: pointer;
  position: relative;
}

.custom-checkbox {
  display: block;
  width: 0.875rem;
  height: 0.875rem;
  background-color: var(--checkbox-background);
  border: 0.0625rem solid var(--checkbox-border);
  border-radius: 0.21875rem;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* Add checked state styling */
.tree-item-checkbox:checked + .custom-checkbox {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.tree-item-checkbox:checked + .custom-checkbox::before {
  content: "";
  position: absolute;
  left: 50%;
  top: calc(50% - 0.0625rem);
  transform: translate(-50%, -50%) rotate(45deg);
  width: 0.25rem;
  height: 0.5625rem;
  border-bottom: 0.0625rem solid var(--text-primary);
  border-right: 0.0625rem solid var(--text-primary);
  z-index: 2;
}

.tree-item-checkbox:indeterminate + .custom-checkbox::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  height: 0.0625rem;
  background-color: var(--text-primary);
  z-index: 2;
}

/* Add hover state for the checkbox */
.tree-item-checkbox-container:hover .custom-checkbox {
  border-color: var(--primary-color);
}

.tree-item-name {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.78125rem;
  letter-spacing: 0.0125rem;
  color: var(--text-primary);
}

.tree-item-tokens {
  font-size: 0.625rem;
  color: var(--text-secondary);
  margin-left: 0.375rem;
  white-space: nowrap;
}

.tree-item-badge {
  font-size: 0.625rem;
  padding: 0.0625rem 0.3125rem;
  border-radius: 0.25rem;
  background-color: var(--hover-color);
  color: var(--text-secondary);
  margin-left: 0.375rem;
  white-space: nowrap;
}

/* Content area styles moved to src/components/content-area.css */

.sort-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: auto;
  min-width: 10.75rem;
  background-color: var(--background-primary);
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
  z-index: 100;
  width: 100%;
}

.sort-option {
  display: flex;
  font-size: 0.6875rem;
  padding: 0.5rem 1rem;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
  color: var(--text-primary);
  width: 100%;
  justify-content: flex-start;
}

.sort-option:hover {
  background-color: var(--hover-color);
}

.sort-option.active {
  background-color: var(--option-selected-bg);
  font-weight: 600;
  color: var(--option-selected-text);
}

/* File stats style moved to src/components/content-area.css */

.file-list-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.file-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: 0.75rem;
  background-color: var(--background-primary);
}

.file-list-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  font-size: 1rem;
  padding: 2rem;
  text-align: center;
}

.file-card {
  display: flex;
  flex-direction: row;
  padding: 0.25rem;
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  position: relative;
  transition: all 0.2s ease;
  background-color: var(--background-primary);
  width: auto;
  max-width: fit-content;
}

.file-card:hover {
  background-color: var(--hover-color);
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.05);
}

.file-card.selected {
  border: 0.125rem solid var(--file-card-selected-border);
  background-color: var(--background-selected);
}

.dark-mode .file-card.selected {
  border: 0.125rem solid var(--file-card-selected-border);
}

.file-card-header {
  display: flex;
  align-items: center;
}

.file-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.25rem;
  color: var(--icon-color);
  flex-shrink: 0;
}

.file-card-name {
  font-size: 0.625rem;
  line-height: 1rem;
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--text-primary);
  max-width: 12.5rem;
  letter-spacing: -0.0125rem;
  position: relative;
  top: 1px;
}

.file-card-info {
  display: flex ;
  flex-direction: column;
  margin-bottom: 0;
  align-items: center;
  justify-content: center;
  margin-left: .5rem;
}

.file-card-line-badge {
  font-size: 0.625rem;
  color: var(--text-primary);
  margin-left: .5rem;
  padding: 0 0.375rem;
  background-color: var(--background-selected);
  border-radius: 4px;
  display: inline-block;
  font-weight: 500;
  text-align: center;
}

.file-card-tokens {
  color: var(--text-secondary);
  font-size: 0.625rem;
  text-align: right;
}

.file-card-status {
  font-size: 0.8125rem;
  color: var(--text-secondary);
}

.file-card-actions {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
  align-items: anchor-center;
  justify-content: end;
  padding-right: .25rem;
}

.file-card:hover .file-card-actions {
  opacity: 1;
}

.file-card-action {
  width: 1.5rem;
  height: 1.5rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-primary);
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  color: var(--icon-color);
  transition: all 0.15s ease;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05);
}

.file-card-action:hover {
  background-color: var(--icon-color);
  color: var(--background-primary);
  border-color: var(--background-primary);
  transform: translateY(-0.0625rem);
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

.file-card-action:active {
  transform: translateY(0);
  box-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.1);
}

.processing-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

/* REMOVED: Unused style - .processing-indicator .content was not referenced in the component */

/* Add styling for the cancel button in processing indicator */
.processing-indicator .cancel-button {
  margin-top: 0.75rem;
  padding: 0.375rem 0.75rem;
  background-color: var(--error-color, #dc3545);
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.processing-indicator .cancel-button:hover {
  background-color: var(--error-hover-color, #bd2130);
}

.spinner {
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 3px solid var(--accent-blue);
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

.progress-bar-container {
  width: 100%;
  height: 10px;
  background-color: var(--background-primary);
  border-radius: 5px;
  overflow: hidden;
  margin-top: 10px;
  position: relative;
  max-width: 18rem;
}

.progress-bar {
  height: 100%;
  background-color: var(--accent-blue);
  border-radius: 5px;
  transition: width 0.3s ease;
}

.progress-details {
  display: block;
  text-align: center;
  margin-top: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--text-primary);
  max-width: 18rem;
  word-wrap: break-word;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-message {
  background-color: rgba(231, 76, 60, 0.1);
  color: var(--error-color);
  padding: 15px;
  margin: 15px;
  border-radius: 4px;
  border-left: 4px solid var(--error-color);
  font-size: 14px;
  line-height: 1.5;
}

/* Welcome Screen Styles */
.welcome-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  height: calc(100vh - 60px);
  overflow: hidden;
  animation: fadeIn 1s ease-in-out;
}

.ascii-logo {
  font-family: monospace;
  font-size: 0.7rem;
  line-height: 1.2;
  white-space: pre;
  text-align: center;
  color: var(--text-secondary);
  transform-origin: center;
  background: var(--background-primary);
  padding: 1.5rem;
  border-radius: 0.5rem;
  position: relative;
}

.welcome-message {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.welcome-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-secondary);
  border: none;
  border-radius: 0.5rem;
  color: var(--text-primary);
  padding: 0.5rem;
  height: 4rem;
  width: 4rem;
  transition: background-color 0.2s ease;
  border: 0.0625rem solid var(--icon-stroke-color);
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.welcome-button:hover {
  background-color: var(--hover-color);
  transform: translateY(-0.125rem);
  box-shadow: 0 0.375rem 0.75rem rgba(0, 0, 0, 0.15);
}

.welcome-button:active {
  transform: translateY(0);
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

.welcome-button svg {
  stroke: var(--icon-stroke-color);
  fill: var(--background-primary);
  stroke-width: 0.09375rem;
  width: 3rem;
  height: 3rem;
}

.welcome-button:hover svg {
  stroke: var(--icon-hover-stroke-color);
}

@keyframes pulsate {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.03);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(1.25rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Shared token recalculation pulse animation */
@keyframes token-recalc-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Token recalculation indicator styles */
.token-recalculating-indicator {
  margin-left: 6px;
  font-size: 0.85em;
  opacity: 0.7;
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.recalc-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
  animation: token-recalc-pulse 1.5s ease-in-out infinite;
}

/* Animation delays for the three dots using explicit classes for robustness */
.recalc-dot-1 {
  /* Default delay: 0s */
}

.recalc-dot-2 {
  animation-delay: 0.3s;
}

.recalc-dot-3 {
  animation-delay: 0.6s;
}

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .recalc-dot {
    animation: none;
    /* Don't set opacity here as parent already has opacity: 0.7 */
  }
}

/* Responsive adjustments for smaller screens */
@media (max-width: 48rem) {
  .ascii-logo {
    font-size: 0.5rem;
  }
  
  .welcome-message h2 {
    font-size: 1.8rem;
  }
  
  .welcome-message p {
    font-size: 1rem;
  }
}


.sidebar-resize-handle {
  position: absolute;
  top: 0;
  left: -0.3125rem;
  width: 0.625rem;
  height: 100%;
  cursor: col-resize;
  padding: 0;
  border: 0;
  width: 6px;
  z-index: 10;
  opacity: 0;
}

.sidebar-resize-handle:hover,
.sidebar-resize-handle:active {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
}

.selected-folder {
  font-size: 0.875rem;
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 31.25rem;
  display: inline-block;
}

/* ===============================================
 * THEME TOGGLE STYLES - MIGRATED TO COMPONENT CSS
 * These styles have been moved to: src/components/theme-toggle.css
 * Migration Date: 2025-08-03
 * Keep commented for rollback if needed
 * =============================================== */

/* macOS-style segmented control for theme toggle */
/* .theme-segmented-control {
  display: flex;
  background-color: var(--background-secondary);
  border-radius: 0.375rem;
  padding: 0.125rem;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.1);
  width: fit-content;
  position: relative;
  height: 2rem;
  overflow: hidden;
  margin-left: auto;
}

.theme-segment {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0;
  padding: 0 0.75rem;
  height: 1.75rem;
  border: none;
  background: none;
  font-size: 0.8125rem;
  font-weight: 500;
  color: var(--text-secondary);
  position: relative;
  z-index: 1;
  transition: color 0.2s ease;
  border-radius: 0.25rem;
  cursor: pointer;
  white-space: nowrap;
  min-width: 4.375rem;
}

.theme-segment:focus {
  outline: none;
}

.theme-segment span {
  font-size: 0.6875rem;
}

.theme-segment.active {
  color: var(--text-primary);
  background-color: var(--background-primary);
  box-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.1);
}

.dark-mode .theme-segment.active {
  background-color: rgba(255, 255, 255, 0.1);
}

.theme-segment {
  transition: background-color 0.2s ease, color 0.2s ease, box-shadow 0.2s ease;
} */

/* MIGRATED TO: src/components/app-header.css */
/* .header-actions styles */

/* Apply Changes Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--background-primary);
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.15);
  max-width: 50rem;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.apply-changes-modal {
  min-height: 25rem;
}

.filter-modal {
  min-height: 25rem;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  border-bottom: 0.0625rem solid var(--border-color);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-primary);
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-primary);
  opacity: 0.7;
}

.close-button:hover {
  opacity: 1;
}

.modal-body {
  padding: 1.25rem;
  overflow-y: auto;
  flex: 1;
}

.modal-description {
  margin-bottom: 1rem;
  color: var(--text-primary);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.625rem;
}

.help-toggle-button {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  margin-left: auto;
  background-color: var(--background-secondary);
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.help-toggle-button:hover {
  background-color: var(--hover-color);
}

.help-text {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: var(--background-secondary);
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  font-size: 0.8125rem;
}

.help-text h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.help-text pre {
  background-color: var(--background-primary);
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  padding: 0.5rem;
  overflow-x: auto;
  font-size: 0.75rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.help-text p {
  margin: 0.5rem 0 0;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.help-text a {
  display: inline-block;
  margin-top: 0.5rem;
  color: var(--accent-blue);
  text-decoration: none;
  font-size: 0.75rem;
}

.help-text a:hover {
  text-decoration: underline;
}

.xml-input {
  width: 100%;
  padding: 0.75rem;
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  font-family: monospace;
  resize: vertical;
  min-height: 12.5rem;
}

.status-message {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 0.25rem;
  background-color: var(--background-secondary);
  white-space: pre-line;
  max-height: 18.75rem;
  overflow-y: auto;
  font-family: monospace;
  line-height: 1.5;
  word-break: break-word;
  border-left: 0.25rem solid var(--border-color);
}

.status-message.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #ff5252;
  border-left: 0.25rem solid #ff5252;
}

.status-message.success {
  background-color: rgba(0, 255, 0, 0.1);
  color: #4caf50;
  border-left: 0.25rem solid #4caf50;
}

/* File paths in status message */
.status-message ul {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  padding-left: 1.25rem;
}

.status-message li {
  margin-bottom: 0.25rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 1rem 1.25rem;
  border-top: 0.0625rem solid var(--border-color);
  gap: 0.75rem;
}

.apply-button, .cancel-button {
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.apply-button {
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  border: none;
}

.apply-button:hover:not(:disabled) {
  background-color: var(--primary-button-background);
}

.apply-button:disabled {
  background-color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.7;
}

.cancel-button {
  background-color: transparent;
  color: var(--text-primary);
  border: 0.0625rem solid var(--border-color);
}

.cancel-button:hover:not(:disabled) {
  background-color: var(--hover-color);
}

.cancel-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Apply XML Changes button in header */
.apply-changes-btn {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
  height: 32px;
}

.apply-changes-btn:hover {
  background-color: var(--primary-button-background);
}

.documentation-link {
  color: var(--accent-blue);
  text-decoration: none;
  font-size: 0.75rem;
}

.tree-loading,
.empty-tree,
.empty-list {
  padding: 1rem;
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 0.5rem;
  height: 0.5rem;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 0.25rem;
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 0.25rem;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* MIGRATED TO: src/components/app-header.css */
/* .folder-icon-app-title styles */

/* Accessibility Utilities */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* MIGRATED TO: src/components/file-view-modal.css */
/* File View Modal Styles - All file-view-modal classes including:
   - .file-view-modal and all variants
   - .file-view-modal-header, -controls, -content, -footer, etc.
   - .selection-mode-radio
   - .line-number selection styles
   Total: ~240 lines migrated */

/* Partial selection indicator in tree view */
.tree-item.partially-selected .tree-item-name {
  font-style: italic;
}

.partial-selection-indicator {
  display: flex;
  margin-left: 6px;
  font-size: 12px;
  color: var(--text-secondary);
  font-style: italic;
}

/* Make file names clickable */
.tree-item-name.clickable {
  cursor: pointer;
  text-decoration: underline;
}
/* 
.tree-item-name.clickable:hover:after {
  content: "";
  position: absolute;
  top: 0;
  left: 1px;
  right: 1px;
  width: auto;
  height: 100%;
  outline: 1.5px solid var(--text-primary);
  border-radius: 4px;
} */

/* File view button */
.tree-item-view-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  border-radius: 4px;
  color: var(--text-secondary);
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tree-item:hover .tree-item-view-btn {
  opacity: 1;
}

.tree-item-view-btn:hover {
  background-color: var(--hover-color);
  color: var(--accent-blue);
}

/* Line info in file card */
.file-card-lines {
  font-size: 0.6875rem;
  color: var(--text-secondary);
  margin-top: 4px;
  font-style: italic;
}

.token-estimate {
  font-size: 13px;
  color: var(--text-secondary);
}

.selection-help {
  font-size: 13px;
  color: var(--text-secondary);
  font-style: italic;
}



/* Prompts Buttons and Modals */
/* Prompts buttons styles moved to src/components/content-area.css */

/* System Prompts Modal */
.system-prompts-modal .modal-body,
.role-prompts-modal .modal-body,
.instructions-modal .modal-body {
  display: flex;
  flex-direction: row;
  padding: 0;
  max-height: calc(90vh - 60px);
  overflow: hidden;
}

.system-prompts-list,
.role-prompts-list,
.instructions-list {
  border: none;
  border-right: 1px solid var(--border-color);
  border-radius: 0;
  margin-bottom: 0;
  max-height: none;
  overflow-y: auto;
  width: 260px;
  background-color: var(--background-secondary);
  flex-shrink: 0;
}

.sidebar.system-prompts-list,
.sidebar.role-prompts-list,
.sidebar.instructions-list {
  padding: 8px 0;
}

.no-prompts-message {
  padding: 32px 16px;
  text-align: center;
  color: var(--text-secondary);
  font-size: 14px;
  background-color: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  margin-top: 12px;
}

.system-prompt-item,
.role-prompt-item,
.instruction-item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: none;
  border-radius: 4px;
  margin: 0 4px 8px 4px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.system-prompt-item:hover,
.role-prompt-item:hover,
.instruction-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.system-prompt-item.selected,
.role-prompt-item.selected,
.instruction-item.selected {
  background-color: var(--background-selected);
}

.prompt-details {
  flex: 1;
  overflow: hidden;
}

.prompt-title {
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--text-primary);
  font-size: 13px;
}

.prompt-preview {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.prompt-actions {
  display: none;
  position: absolute;
  right: 0.5rem;
  top: 0.375rem;
  gap: 0.25rem;
}

.prompt-actions button {
  height: 1.375rem;
}

.system-prompt-item:hover .prompt-actions,
.role-prompt-item:hover .prompt-actions,
.instruction-item:hover .prompt-actions {
  display: flex;
}

.prompt-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
  border: none;
  background-color: transparent;
  color: var(--icon-color);
  cursor: pointer;
  transition: background-color 0.2s;
}

.prompt-action-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.toggle-selection-button.selected {
  color: var(--success-color);
}

.delete-button:hover {
  color: var(--error-color);
}

/* Content area editor styles moved to src/components/content-area.css */

.add-prompt-form {
  height: 100%;
}

.edit-prompt-form h3,
.add-prompt-form h3 {
  font-size: 16px;
  font-weight: 500;
  margin-top: 0;
  margin-bottom: 16px;
  color: var(--text-primary);
}

.prompt-title-input, 
.prompt-content-input {
  width: 100%;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: white;
  color: var(--text-primary);
  font-family: inherit;
}

.prompt-content-input {
  width: 100%;
  height: 100%;
  padding: 1rem;
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.375rem;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  height: calc(100% - 6rem);
  transition: all 0.2s ease;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05);
  outline: none;
}

.prompt-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.prompt-add-action {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.prompt-add-action  h3 {
  margin: 0;
}

.add-prompt-button {
  display: flex;
  align-items: center;
  gap: .5rem;
  padding: 0.375rem 0.75rem;
  background-color: var(--background-secondary);
  border: none;
  border-radius: 0.25rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: background-color 0.2s;
  height: 2rem;
  margin-left: auto;
}

.notes-app-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.notes-app-layout .modal-header {
  border-bottom: 1px solid var(--border-color);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--background-secondary);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.notes-app-layout .modal-header h2 {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.system-prompts-list,
.role-prompts-list,
.instructions-list {
  border: none;
  border-right: 1px solid var(--border-color);
  border-radius: 0;
  margin-bottom: 0;
  max-height: none;
  overflow-y: auto;
  width: 260px;
  background-color: var(--background-secondary);
  flex-shrink: 0;
}

/* Workspace Modal Styles */
.workspace-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}


.workspace-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 400px;
  overflow-y: auto;
  padding: 4px;
  background-color: var(--background-secondary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.workspace-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  border-radius: 4px;
  background-color: var(--background-primary);
  transition: all 0.15s ease;
  border: 1px solid transparent;
  position: relative;
}

.workspace-item.draggable {
  cursor: grab;
}

.workspace-item.draggable:hover {
  background-color: var(--bg-tertiary);
}

.workspace-item.dragging {
  opacity: var(--workspace-drag-opacity);
  cursor: grabbing;
  z-index: 1000;
}

.workspace-item.drag-over {
  position: relative;
}

.workspace-item.drag-over::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--accent-primary);
  box-shadow: 0 0 4px var(--accent-primary);
  animation: pulse 0.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.drag-handle {
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  opacity: 0.6;
  margin-right: 8px;
  cursor: grab;
  user-select: none;
}

.drag-handle:hover {
  opacity: 1;
}

.workspace-item.dragging .drag-handle {
  cursor: grabbing;
}

.workspace-item:hover {
  background-color: var(--hover-color);
  border-color: var(--border-color);
}

.workspace-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

.workspace-item .prompt-details {
  flex: 1;
  min-width: 0;
}

.workspace-item .prompt-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


/* Workspace action buttons */
.workspace-actions .prompt-action-button {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  height: 28px;
  min-width: auto;
}

.workspace-actions .prompt-action-button:first-child {
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  padding: 4px 12px;
}

.workspace-actions .prompt-action-button:first-child:hover {
  background-color: var(--accent-blue);
}

.workspace-actions .rename-button,
.workspace-actions .delete-button {
  padding: 4px;
  min-width: 28px;
}

.workspace-actions .confirm-button {
  color: var(--success-color);
}

.workspace-actions .cancel-button {
  color: var(--error-color);
}

/* Rename input within workspace item */
.workspace-item .prompt-title-input {
  background-color: var(--background-primary);
  border: 1px solid var(--accent-blue);
  padding: 4px 8px;
  font-size: 14px;
  margin: 0;
}

/* Workspace checkbox styles */

.workspace-checkbox-container {
  margin-right: 12px;
  display: flex;
  align-items: center;
  position: relative;
  width: 14px;
  height: 14px;
}

/* XML Editor container */
.xml-editor-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
}

/* Make textarea styling consistent with prompts modal */
.xml-input.prompt-content-input {
  font-family: monospace;
  resize: vertical;
  min-height: 12.5rem;
}
