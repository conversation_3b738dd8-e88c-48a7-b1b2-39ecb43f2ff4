/* Auto-save toggle switch styles */

.auto-save-toggle-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auto-save-toggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  user-select: none;
}

.auto-save-toggle-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.auto-save-toggle-slider {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2.75rem;
  height: 1.5rem;
  background-color: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  transition: background-color 0.2s ease, border-color 0.2s ease;
}

.auto-save-toggle-slider::before {
  content: '';
  position: absolute;
  left: 0.125rem;
  width: 1.25rem;
  height: 1.25rem;
  background-color: var(--background-primary);
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
}

.auto-save-icon {
  position: relative;
  z-index: 1;
  color: var(--text-secondary);
  transition: color 0.2s ease;
  opacity: 0.6;
  margin-left: 1.25rem;
}

/* When enabled */
.auto-save-toggle-input:checked + .auto-save-toggle-slider {
  background-color: var(--accent-color, #3b82f6);
  border-color: var(--accent-color, #3b82f6);
}

.auto-save-toggle-input:checked + .auto-save-toggle-slider::before {
  transform: translateX(1.25rem);
}

.auto-save-toggle-input:checked + .auto-save-toggle-slider .auto-save-icon {
  color: white;
  opacity: 1;
  margin-left: 0;
  margin-right: 1.25rem;
}

/* Hover state */
.auto-save-toggle:hover .auto-save-toggle-slider {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Focus state */
.auto-save-toggle-input:focus-visible + .auto-save-toggle-slider {
  outline: 2px solid var(--accent-color, #3b82f6);
  outline-offset: 2px;
}

/* Label text */
.auto-save-label {
  font-size: 0.8125rem;
  font-weight: 500;
  color: var(--text-secondary);
  white-space: nowrap;
}

/* Disabled state (if needed later) */
.auto-save-toggle-input:disabled + .auto-save-toggle-slider {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Dark mode adjustments */
.dark-mode .auto-save-toggle-slider {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .auto-save-toggle-input:checked + .auto-save-toggle-slider {
  background-color: var(--accent-color, #3b82f6);
  border-color: var(--accent-color, #3b82f6);
}