{"extends": "./tsconfig.base.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "jsx": "react-jsx", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "isolatedModules": true, "noEmit": true, "skipLibCheck": true, "types": ["vite/client", "node"], "jsxImportSource": "react", "checkJs": false, "allowSyntheticDefaultImports": true, "useDefineForClassFields": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/types/**/*.d.ts", "src/declarations.d.ts"], "exclude": ["src/main/**", "src/**/__tests__/**", "src/**/__mocks__/**"], "references": [{"path": "./tsconfig.node.json"}]}